﻿import pickle
import numpy as np
from numpy import linalg as la 
import sys

maxlen=128

def load_data(filename=r'.\RML2016.10a_dict.pkl', seed=2016, train_ratio=0.6, val_ratio=0.2):

    # load data

    #Xd(120W,2,128) 10calss*20SNR*6000samples
    Xd =pickle.load(open(filename,'rb'),encoding='iso-8859-1')

    #mods['8PSK', 'AM-DSB', 'BPSK', 'CPFSK', 'GFSK', 'PAM4', 'QAM16', 'QAM64', 'QPSK', 'WBFM']
    mods, snrs, rates = [sorted(list(set([k[j] for k in Xd.keys()]))) for j in [0, 1, 2]]

    # gen train, val, test set
    X = []
    lbl = []
    train_idx=[]
    val_idx=[]
    np.random.seed(seed)
    a=0
    for mod in mods:
        for snr in snrs:
            for rate in rates:
                X.append(Xd[(mod, snr, rate)])     #ndarray(6000,2,128)

                dataset = Xd[(mod, snr, rate)].shape[0]
                trainset = int(dataset*train_ratio)
                valset = int(dataset*val_ratio)
                #testset = int(dataset*0.2) 
                #remain all the samples as testset

                for i in range(Xd[(mod, snr, rate)].shape[0]):
                    lbl.append((mod, snr, rate))
                train_idx+=list(np.random.choice(range(a*dataset,(a+1)*dataset), size=trainset, replace=False))
                val_idx+=list(np.random.choice(list(set(range(a*dataset,(a+1)*dataset))-set(train_idx)), size=valset, replace=False))
                a+=1
    X = np.vstack(X)
    n_examples=X.shape[0]

    test_idx = list(set(range(0,n_examples))-set(train_idx)-set(val_idx))
    np.random.shuffle(train_idx)
    np.random.shuffle(val_idx)
    np.random.shuffle(test_idx)

    X_train = X[train_idx]
    X_val   = X[val_idx]
    X_test  = X[test_idx]

    X_train = X_train.swapaxes(2, 1)
    X_val   = X_val.swapaxes(2, 1)
    X_test  = X_test.swapaxes(2, 1)

    # transfor the label form to one-hot
    def to_onehot(yy):
        yy1 = np.zeros([len(yy), len(mods)])
        yy1[np.arange(len(yy)), yy] = 1

        return yy1

    Y_train = to_onehot(list(map(lambda x: mods.index(lbl[x][0]), train_idx)))
    Y_val   = to_onehot(list(map(lambda x: mods.index(lbl[x][0]), val_idx)))
    Y_test  = to_onehot(list(map(lambda x: mods.index(lbl[x][0]), test_idx)))

    return (mods,snrs,rates,lbl),(X_train,Y_train),(X_val,Y_val),(X_test,Y_test),(train_idx,val_idx,test_idx)

if __name__ == '__main__':
    (mods, snrs, rates, lbl), (X_train, Y_train),(X_val,Y_val), (X_test, Y_test), (train_idx,val_idx,test_idx) = load_data(filename=r'./build-dataset/RadioSamples-rx1024.dat')