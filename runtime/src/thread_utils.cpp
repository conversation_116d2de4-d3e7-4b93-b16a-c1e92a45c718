#include "thread_utils.h"
#include <thread>
#include <string>
#include <pthread.h>
#include <sys/syscall.h>

#include "logger.h"


void SetThreadName(std::thread& thread, std::string name) {
    auto err = pthread_setname_np(thread.native_handle(), name.c_str());
    if (err != 0) {
        Logger::instance().error("pthread_setname_np failed: {}", strerror(err));
    }
}

void SetThreadAffinity(std::thread& thread, int cpuID) {
    pthread_t native_thread = thread.native_handle();
    cpu_set_t cpuset{};
    CPU_ZERO(&cpuset);
    CPU_SET(cpuID, &cpuset);

    auto err =
        pthread_setaffinity_np(native_thread, sizeof(cpu_set_t), &cpuset);
    if (err != 0) {
        Logger::instance().error("pthread_setaffinity_np failed: {}",
                             strerror(err));
    }
}

void SetThreadPriority(std::thread& thread, int priority) {
    // 获取线程的 native_handle
    pthread_t native_thread = thread.native_handle();

    // 设置调度策略为 SCHED_FIFO（实时优先级调度）
    int policy = SCHED_FIFO;
    sched_param param;
    param.sched_priority = priority;

    // 设置线程的优先级
    auto err = pthread_setschedparam(native_thread, policy, &param);
    if (err != 0) {
        Logger::instance().error("pthread_setschedparam failed: {}", strerror(err));
    }
}