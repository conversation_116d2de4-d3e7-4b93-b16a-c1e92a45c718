#include "classifier.h"

#include <codecvt>
#include <cstring>
#include <iostream>
#include <locale>
#include <regex>

#include <onnxruntime_cxx_api.h>

#include "algo.h"
#include "config.h"
#include "logger.h"

using namespace std;

Classifier::Classifier(ClassifierConfig config) {
    CreateSession(config.model, config.provider);
}

Classifier::~Classifier() {
    // 顺序有要求
    this->input_node_names_char.clear();
    this->input_node_names.clear();

    this->output_node_names_char.clear();
    this->output_node_names.clear();

    this->session.reset();
}

void Classifier::CreateSession(std::string onnx_path_string,
                               std::string provider) {
    // 检查模型路径中是否包含中文字符
    std::regex pattern("[\u4e00-\u9fa5]");
    bool result = std::regex_search(onnx_path_string, pattern);
    if (result) {
        Logger::instance().info("Your model path is error. Change your model "
                                "path without chinese characters.");

        return;
    }

    // 加载模型
    Ort::Env env = Ort::Env(ORT_LOGGING_LEVEL_ERROR, "ONNXRuntime");

    // Create an ONNX Runtime session with CUDA
    Ort::SessionOptions session_options;
    session_options.SetGraphOptimizationLevel(
        GraphOptimizationLevel::ORT_ENABLE_ALL);
    session_options.SetIntraOpNumThreads(1);
    session_options.SetGraphOptimizationLevel(
        GraphOptimizationLevel::ORT_ENABLE_EXTENDED);

    if (provider == "CPU") {
        Logger::instance().info("CPU provider.");

    } else if (provider == "CUDA") {
        Logger::instance().info("CUDA provider.");

        // Enable CUDA
        OrtCUDAProviderOptions cuda_options;
        cuda_options.device_id             = 0;
        cuda_options.arena_extend_strategy = 0;
        session_options.AppendExecutionProvider_CUDA(cuda_options);

    } else if (provider == "TensorRT") {
        Logger::instance().info("TensorRT provider.");

        // Enable TensorRT
        OrtTensorRTProviderOptions tensorrt_options{};
        tensorrt_options.device_id                             = 0;
        tensorrt_options.trt_max_workspace_size                = **********;
        tensorrt_options.trt_max_partition_iterations          = 10;
        tensorrt_options.trt_min_subgraph_size                 = 5;
        tensorrt_options.trt_fp16_enable                       = 0;
        tensorrt_options.trt_int8_enable                       = 0;
        tensorrt_options.trt_int8_use_native_calibration_table = 0;
        tensorrt_options.trt_dump_subgraphs                    = 1;
        tensorrt_options.trt_engine_cache_enable               = 1;
        tensorrt_options.trt_engine_cache_path = "./tensorrt_cache";
        // tensorrt_options.trt_timing_cache_enable = 1;
        // tensorrt_options.trt_timing_cache_path = "./tensorrt_cache";
        session_options.AppendExecutionProvider_TensorRT(tensorrt_options);
    }

    // Load the ONNX model
    this->session.reset(
        new Ort::Session(env, onnx_path_string.c_str(), session_options));

    // Get input and output node information
    Ort::AllocatorWithDefaultOptions allocator;
    size_t inputCount = session->GetInputCount();
    for (size_t i = 0; i < inputCount; i++) {
        auto temp = session->GetInputNameAllocated(i, allocator);
        auto name = std::string(temp.get());
        this->input_node_names.push_back(name);
    }

    for (auto& item : this->input_node_names) {
        this->input_node_names_char.push_back(item.c_str());
        Logger::instance().info("input : {}.", item);
    }

    size_t outputCount = session->GetOutputCount();
    for (size_t i = 0; i < outputCount; i++) {
        auto temp = session->GetOutputNameAllocated(i, allocator);
        auto name = std::string(temp.get());
        this->output_node_names.push_back(name);
    }

    for (auto& item : this->output_node_names) {
        this->output_node_names_char.push_back(item.c_str());
        Logger::instance().info("output : {}.", item);
    }
    this->options = Ort::RunOptions{nullptr};
}

void Classifier::TensorProcess(Classifier::InTensor& input,
                               Classifier::InShape& input_shape,
                               std::vector<std::vector<DL_RESULT>>& results) {

    Ort::MemoryInfo memoryInfo =
        Ort::MemoryInfo::CreateCpu(OrtArenaAllocator, OrtMemTypeDefault);

    Ort::Value inputTensor = Ort::Value::CreateTensor<float>(
        memoryInfo, reinterpret_cast<float*>(input.data()), input.size() * 2,
        input_shape.data(), input_shape.size());
    auto outputTensors =
        session->Run(options, this->input_node_names_char.data(), &inputTensor,
                     1, this->output_node_names_char.data(),
                     this->output_node_names_char.size());

    std::size_t idx = 0;
    for (auto& outputItem : outputTensors) {
        auto output       = outputItem.GetTensorMutableData<float>();
        auto output_shape = outputItem.GetTensorTypeAndShapeInfo().GetShape();
        auto dims         = static_cast<std::size_t>(output_shape.at(1));
        // softmax(output, dims);

        std::vector<DL_RESULT> result_list;
        for (std::size_t i = 0; i < dims; i++) {
            result_list.push_back(
                {.dimId = idx, .classId = i, .confidence = output[i]});
        }
        results.push_back(result_list);
        idx++;
    }
}

void Classifier::BatchTensorProcess(
    Classifier::InTensor& input,
    Classifier::InShape& input_shape,
    std::vector<std::vector<DL_RESULT>>& results) {

    Ort::MemoryInfo memoryInfo =
        Ort::MemoryInfo::CreateCpu(OrtArenaAllocator, OrtMemTypeDefault);

    Ort::Value inputTensor = Ort::Value::CreateTensor<float>(
        memoryInfo, reinterpret_cast<float*>(input.data()), input.size() * 2,
        input_shape.data(), input_shape.size());
    auto outputTensors =
        session->Run(options, this->input_node_names_char.data(), &inputTensor,
                     1, this->output_node_names_char.data(),
                     this->output_node_names_char.size());

    std::size_t batch_size = input_shape[0];
    std::size_t idx        = 0;
    for (auto& outputItem : outputTensors) {
        std::vector<DL_RESULT> task_results;
        auto output       = outputItem.GetTensorMutableData<float>();
        auto output_shape = outputItem.GetTensorTypeAndShapeInfo().GetShape();
        auto dims         = static_cast<std::size_t>(output_shape.at(1));

        for (std::size_t b = 0; b < batch_size; b++) {
            DL_RESULT task_result{0, 0, 0.0f};
            for (std::size_t i = 0; i < dims; i++) {
                auto confidence = output[b * dims + i];
                if (confidence > task_result.confidence) {
                    task_result.dimId      = idx;
                    task_result.classId    = i;
                    task_result.confidence = confidence;
                }
            }
            task_results.push_back(task_result);
        }
        results.push_back(task_results);
        idx++;
    }
}

void Classifier::Predict(Classifier::InTensor& input_data,
                         std::vector<std::vector<DL_RESULT>>& result) {
    Classifier::InShape input_shape{1, N, 2};
    this->TensorProcess(input_data, input_shape, result);
}

void Classifier::BatchPredict(Classifier::InTensor& input_data,
                              std::vector<std::vector<DL_RESULT>>& result) {

    Classifier::InShape input_shape{B, N, 2};
    this->BatchTensorProcess(input_data, input_shape, result);
}
