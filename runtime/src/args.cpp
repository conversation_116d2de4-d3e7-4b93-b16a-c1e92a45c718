#include "args.h"

#include <args.hxx>
#include <iostream>

std::tuple<bool, Arguments> ParseArgs(int argc, char** argv) {
    args::ArgumentParser parser("hhgroup radar signal processing program.",
                                "This goes after the options.");
    args::HelpFlag help(parser, "help", "Display this help menu",
                        {'h', "help"});
    args::ValueFlag<std::string> workDirArg(
        parser, "workdir", "Set working directory", {'w', "workDir"}, "");
    try {
        parser.ParseCLI(argc, argv);
    } catch (args::Help& e) {
        std::cout << parser;
        return std::tuple<bool, Arguments>{false, Arguments{}};
    } catch (args::ParseError& e) {
        std::cerr << e.what() << std::endl;
        std::cerr << parser;
        return std::tuple<bool, Arguments>{false, Arguments{}};
    } catch (args::ValidationError& e) {
        std::cerr << e.what() << std::endl;
        std::cerr << parser;
        return std::tuple<bool, Arguments>{false, Arguments{}};
    }

    std::string workDir = args::get(workDirArg);
    return std::tuple<bool, Arguments>{true, Arguments{workDir}};
}