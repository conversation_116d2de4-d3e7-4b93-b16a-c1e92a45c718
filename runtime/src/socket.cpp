#include "socket.h"
#include "logger.h"

namespace DataFlow {

Socket::Socket(SocketType socket_type) : m_socket(), m_addr() {
    m_socket = ::socket(AF_INET, static_cast<int>(socket_type), 0);
    if (m_socket == INVALID_SOCKET) {
        throw std::runtime_error("Could not create socket");
    }

    m_addr.sin_family = AF_INET;
}
Socket::~Socket() {
    shutdown(m_socket, SHUT_RDWR);
    ::close(m_socket);
}
void Socket::setPort(std::uint16_t port) {
    m_addr.sin_port = ::htons(port);
}

void Socket::setOpt() {

    int recvbuffsize = 0;
    int optsize      = sizeof(recvbuffsize);

    auto err = ::getsockopt(m_socket, SOL_SOCKET, SO_RCVBUF, &recvbuffsize,
                            (socklen_t*) &optsize);
    if (err == SOCKET_ERROR) {
        Logger::instance().error("getsockopt SO_RCVBUF error {}", err);
    }

    recvbuffsize = 128 * 1024 * 1024;
    err          = ::setsockopt(m_socket, SOL_SOCKET, SO_RCVBUF,
                                &recvbuffsize, sizeof(recvbuffsize));
    if (err == SOCKET_ERROR) {
        Logger::instance().error("setsockopt SO_RCVBUF error {}", err);
    }

    recvbuffsize = 0;
    optsize      = sizeof(recvbuffsize);
    err          = ::getsockopt(m_socket, SOL_SOCKET, SO_RCVBUF,
                                &recvbuffsize, (socklen_t*) &optsize);
    if (err == SOCKET_ERROR) {
        Logger::instance().error("getsockopt SO_RCVBUF error {}", err);
    }
}

int Socket::setAddress(const std::string& address) {
    return ::inet_pton(AF_INET, address.c_str(), &m_addr.sin_addr);
}

int Socket::bind() {
    return ::bind(m_socket, reinterpret_cast<sockaddr*>(&m_addr),
                  sizeof(m_addr));
}
int Socket::connect() {
    return ::connect(m_socket, reinterpret_cast<sockaddr*>(&m_addr),
                     sizeof(m_addr));
}

SOCKET Socket::get() const {
    return m_socket;
}

int UDPServer::Listen(const std::string& address, std::uint16_t port) {
    listenSocket_.setPort(port);
    listenSocket_.setAddress(address);
    int err = listenSocket_.bind();
    if (err == SOCKET_ERROR) {
        return socket_bind_err;
    }

    listenSocket_.setOpt();
    return 0;
}
int UDPServer::Recv(char* buffer, int len) {
    // auto remote_addr = sockaddr_in{};
    // socklen_t size = sizeof(remote_addr);

    ssize_t recv_len = ::recvfrom(listenSocket_.get(), buffer, len, 0, 0,
                                  0); //(struct sockaddr *)&remote_addr, &size);
    if (recv_len == SOCKET_ERROR) {
        return receive_err;
    }

    if (recv_len != len) {
        return receive_not_enough_err;
    }

    return 0;
}

int UDPClient::Connect(const std::string& address, std::uint16_t port) {
    clientSocket_.setPort(port);
    if (clientSocket_.setAddress(address) <= 0) {
        throw std::runtime_error("address is not available");
    }

    clientSocket_.connect();
    return 0;
}

int UDPClient::Send(const char* buffer, int len) {
    ssize_t sent_len = ::send(clientSocket_.get(), buffer, len, 0);
    if (sent_len == SOCKET_ERROR) {
        return send_err;
    }

    if (sent_len != len) {
        return send_not_enough_err;
    }

    return 0;
}

int UDPClient::SendTo(const char* buffer, int len, std::string addrStr,
                      std::uint16_t port) {
    struct sockaddr_in addr{};
    socklen_t addr_len = sizeof(addr);

    addr.sin_family = AF_INET;
    addr.sin_port   = ::htons(port);
    if (::inet_pton(AF_INET, addrStr.c_str(), &addr.sin_addr) <= 0) {
        throw std::runtime_error("Invalid address");
    }

    ssize_t sent_len = ::sendto(clientSocket_.get(), buffer, len, 0,
                                (struct sockaddr*) &addr, addr_len);
    if (sent_len == SOCKET_ERROR) {
        return send_err;
    }

    if (sent_len != len) {
        return send_not_enough_err;
    }

    return 0;
}

int TCPClient::Connect(const std::string& address, std::uint16_t port) {
    clientSocket_.setPort(port);
    if (clientSocket_.setAddress(address) <= 0) {
        throw std::runtime_error("address is not available");
    }

    clientSocket_.setOpt();
    if (clientSocket_.connect() == SOCKET_ERROR) {
        return connect_err;
    }
    return 0;
}

int TCPClient::Send(const char* buffer, std::size_t len) {
    ssize_t sent_len = ::send(clientSocket_.get(), buffer, len, MSG_NOSIGNAL);
    if (sent_len == SOCKET_ERROR) {
        return send_err;
    }

    if (sent_len != (ssize_t)len) {
        return send_not_enough_err;
    }

    return 0;
}

int TCPClient::Recv(char* buffer, std::size_t len) {
    ssize_t recv_len =
        ::recv(clientSocket_.get(), buffer, len, MSG_NOSIGNAL | MSG_WAITALL);
    if (recv_len == SOCKET_ERROR) {
        return receive_err;
    }

    if (recv_len != (ssize_t)len) {
        return receive_not_enough_err;
    }

    return 0;
}

} // namespace DataFlow