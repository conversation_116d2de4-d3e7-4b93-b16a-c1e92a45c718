#include <format>
#include <fstream>
#include <string>
#include <vector>

#include <csv.hpp>
#include <gtest/gtest.h>
#include <nlohmann/json.hpp>
#include <onnxruntime_cxx_api.h>

#include "algo.h"
#include "classifier.h"
#include "config.h"
#include "logger.h"
#include "malloc_type.h"
#include "structs.h"

// Function to read CSV file
std::vector<RADIO_ENTRY> readCSV(const std::string& filePath) {
    std::vector<RADIO_ENTRY> entries;
    io::CSVReader<6> in(filePath);
    in.read_header(io::ignore_extra_column, "Modulation", "label", "SNR",
                   "Rate", "I_Samples", "Q_Samples");

    std::string modulation{};
    std::size_t label{};
    int snr{};
    std::size_t rate{};
    std::string i_sample_text{}, q_sample_text{};
    sample_t sample{};

    while (in.read_row(modulation, label, snr, rate, i_sample_text,
                       q_sample_text)) {
        RADIO_ENTRY entry;
        entry.modulation = modulation;
        entry.label      = label;
        entry.snr        = snr;
        entry.rate       = rate;

        std::vector<float> i_samples;
        std::vector<float> q_samples;
        std::stringstream i_ss(i_sample_text);
        std::stringstream q_ss(q_sample_text);
        std::string i_token, q_token;

        while (std::getline(i_ss, i_token, '|')) {
            i_samples.push_back(std::stof(i_token));
        }

        while (std::getline(q_ss, q_token, '|')) {
            q_samples.push_back(std::stof(q_token));
        }

        for (std::size_t i = 0; i < Classifier::N; ++i) {
            entry.sample(i) = std::complex<float>(i_samples[i], q_samples[i]);
        }
        entries.push_back(entry);
    }

    return entries;
}

void CheckStatus(OrtStatus* status, const OrtApi* g_ort) {
    if (status != NULL) {
        const char* msg = g_ort->GetErrorMessage(status);
        std::cout << msg << std::endl;
        g_ort->ReleaseStatus(status);
        exit(1);
    }
}

void PrintOnnxProvider() {
    const OrtApi* g_ort = OrtGetApiBase()->GetApi(ORT_API_VERSION);
    int len;
    char** providers;
    std::cout << "Fetching list of providers." << std::endl;
    CheckStatus(g_ort->GetAvailableProviders(&providers, &len), g_ort);
    std::cout << std::format("Number of available providers = {}", len)
              << std::endl;
    std::cout << "List of providers:" << std::endl;
    std::cout << "-----------------" << std::endl;
    for (int i = 0; i < len; i++)
        std::cout << std::format("{}", providers[i]) << std::endl;
    std::cout << "-----------------" << std::endl;
    std::cout << "Cleaning up allocated resources." << std::endl;
    CheckStatus(g_ort->ReleaseAvailableProviders(providers, len), g_ort);
}

// Function to prepare single input
std::vector<float> prepareSingle(const std::vector<float>& input) {
    // Dummy implementation for testing
    return input;
}

// Test fixture class
class ModelTest : public ::testing::Test {
  protected:
    void SetUp() override {
        init_algo();

        std::filesystem::path cwd = std::filesystem::current_path();
        std::cout << "Current working directory: " << cwd << std::endl;

        LogConfig logConfig{.path     = "./logs",
                            .level    = "info",
                            .maxSize  = 1024 * 1024,
                            .maxFiles = 1024,
                            .console  = true};
        Logger::instance().start(logConfig, "radio_unitest");
        PrintJemallocVersion();
        PrintOnnxProvider();

        ClassifierConfig classifierConfig{.model = "./onnx_model/DAE_1024.onnx",
                                          .provider = "CUDA"};
        cls_     = std::make_shared<Classifier>(classifierConfig);
        entries_ = readCSV(data_path_);

        for (auto& entry : entries_) {
            prepare_data<1, Classifier::N>(entry.sample);
        }

        prepared_entries_ = readCSV(prepared_path_);
    }

    const std::string model_path_    = "./onnx_model/DAE_1024.onnx";
    const std::string data_path_     = "./test_1024.csv";
    const std::string prepared_path_ = "./test_prepared_1024.csv";
    std::vector<RADIO_ENTRY> entries_;
    std::vector<RADIO_ENTRY> prepared_entries_;
    std::shared_ptr<Classifier> cls_;
};

// Test case for model inference
TEST_F(ModelTest, Print) {
    auto idx = 0;
    std::cout << "prepared_entries_[" << idx << "] matrix:" << std::endl;
    std::cout << std::scientific << std::setprecision(7) << prepared_entries_[idx].sample << std::endl;


}

// Test case for model inference
TEST_F(ModelTest, ModelInference) {
    int pass_count = 0;
    int fail_count = 0;

    // 推理
    for (auto [count, entry] : entries_ | std::views::enumerate) {
        std::vector<DL_RESULT> result_list{};
        std::vector<std::vector<DL_RESULT>> all_result;
        cls_->Predict(entry.sample, all_result);

        for (auto& task_results : all_result) {
            DL_RESULT task_result{0, 0, 0.0f};
            for (auto& res : task_results) {
                if (res.confidence > task_result.confidence) {
                    task_result = res;
                }
            }
            result_list.push_back(task_result);
        }
        auto res = result_list[0];
        // std::cout << std::format("index: {}, predict: {}, label: {}, equal:
        // {}",
        //                          count, res.classId, entry.label,
        //                          res.classId == entry.label)
        //           << std::endl;

        // if (res.classId != entry.label) {
        //     std::cout << std::format("index: {}", count) << std::endl;
        //     std::cout << "entry.sample matrix:" << std::endl;
        //     std::cout << entry.sample << std::endl;
        //     std::cout << "prepared.sample matrix:" << std::endl;
        //     std::cout << prepared_entries_[count].sample << std::endl;
        //     std::cout << std::endl;
        // }

        EXPECT_EQ(res.classId, entry.label);
        if (res.classId == entry.label) {
            pass_count++;
        } else {
            fail_count++;
        }
    }

    std::cout << std::format("Total Passed: {}", pass_count) << std::endl;
    std::cout << std::format("Total Failed: {}", fail_count) << std::endl;
    float success_rate =
        static_cast<float>(pass_count) / (pass_count + fail_count);
    std::cout << std::format("Success Rate: {:.2f}%", success_rate * 100)
              << std::endl;
}

// Test case for batch model inference
TEST_F(ModelTest, BatchModelInference) {

    // 推理
    auto batchTensor =
        std::make_shared<Classifier::InTensor>(Classifier::B, Classifier::N);
    std::vector<RADIO_ENTRY> inputEntries;
    int entry_idx = 0;
    for (std::size_t idx = 0; idx < Classifier::B; idx++) {
        auto entry = entries_[entry_idx];
        std::memcpy(batchTensor->data() + idx * Classifier::N,
                    entry.sample.data(),
                    Classifier::N * sizeof(std::complex<float>));
        entry_idx = (entry_idx + 1) % entries_.size();
        inputEntries.push_back(entry);
    }

    auto start = std::chrono::high_resolution_clock::now();
    std::vector<std::vector<DL_RESULT>> batch_result;
    cls_->BatchPredict(*batchTensor, batch_result);
    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double> elapsed = end - start;
    std::cout << "Elapsed time: " << elapsed.count() << " seconds" << std::endl;

    int pass_count = 0;
    int fail_count = 0;
    for (auto [count, res] : batch_result[0] | std::views::enumerate) {
        auto entry = inputEntries[count];
        if (res.classId == entry.label) {
            pass_count++;
        } else {
            fail_count++;
        }

        // std::cout << std::format("index: {}, predict: {}, label: {}, equal:
        // {}",
        //                          count, res.classId, entry.label,
        //                          res.classId == entry.label)
        //           << std::endl;
        EXPECT_EQ(res.classId, entry.label);
    }

    std::cout << std::format("Total Passed: {}", pass_count) << std::endl;
    std::cout << std::format("Total Failed: {}", fail_count) << std::endl;
    float success_rate =
        static_cast<float>(pass_count) / (pass_count + fail_count);
    std::cout << std::format("Success Rate: {:.2f}%", success_rate * 100)
              << std::endl;
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}