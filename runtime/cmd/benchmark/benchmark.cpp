#include <algorithm>
#include <benchmark/benchmark.h>
#include <filesystem>
#include <iostream>
#include <queue>
#include <string>
#include <vector>

#include <csv.hpp>

#include "algo.h"
#include "classifier.h"
#include "structs.h"
#include "logger.h"
#include "malloc_type.h"
#include "config.h"

using namespace std;

std::vector<RADIO_ENTRY> entries;
Classifier::InTensor batchTensor{MaxWindow, MaxSample};
std::queue<std::shared_ptr<Classifier>> clss;
std::mutex mtx;

std::shared_ptr<Classifier> getClassifier() {
    std::lock_guard<std::mutex> lock(mtx);
    auto cls = clss.front();
    clss.pop();
    return cls;
}

void pushClassifier(std::shared_ptr<Classifier> cls) {
    std::lock_guard<std::mutex> lock(mtx);
    clss.push(cls);
}

// Preload function
void Preload() {
    init_algo();

    std::string word;
    std::filesystem::path current_working_dir = std::filesystem::current_path();
    cout << "Current working directory: " << current_working_dir << endl;

    LogConfig logConfig{.path     = "./logs",
                        .level    = "info",
                        .maxSize  = 1024 * 1024,
                        .maxFiles = 1024,
                        .console  = true};
    Logger::instance().start(logConfig, "radio_unitest");

    PrintJemallocVersion();

    io::CSVReader<6> in("test_1024.csv");
    in.read_header(io::ignore_extra_column, "Modulation", "label", "SNR",
                   "Rate", "I_Samples", "Q_Samples");

    std::string modulation{};
    std::size_t label{};
    int snr{};
    std::size_t rate{};
    std::string i_sample_text{}, q_sample_text{};
    sample_t sample;

    while (in.read_row(modulation, label, snr, rate, i_sample_text,
                       q_sample_text)) {
        RADIO_ENTRY entry;
        entry.modulation = modulation;
        entry.label      = label;
        entry.snr        = snr;
        entry.rate       = rate;

        std::vector<float> i_samples;
        std::vector<float> q_samples;
        std::stringstream i_ss(i_sample_text);
        std::stringstream q_ss(q_sample_text);
        std::string i_token, q_token;

        while (std::getline(i_ss, i_token, '|')) {
            i_samples.push_back(std::stof(i_token));
        }

        while (std::getline(q_ss, q_token, '|')) {
            q_samples.push_back(std::stof(q_token));
        }

        for (std::size_t i = 0; i < Classifier::N; ++i) {
            entry.sample(i) = std::complex<float>(i_samples[i], q_samples[i]);
        }
        entries.push_back(entry);
    }

    // 加载路径

    ClassifierConfig config{.model    = "./onnx_model/DAE_1024.onnx",
                            .provider = "CUDA"};
    // 加载模型
    for (int i = 0; i < 4; i++) {
        auto cls = std::make_shared<Classifier>(config);
        clss.push(cls);
    }

    for (auto& entry : entries) {
        prepare_data<1, Classifier::N>(entry.sample);
    }

    std::size_t entry_idx = 0;
    for (std::size_t idx = 0; idx < Classifier::B; idx++) {
        auto entry = entries[entry_idx];
        std::memcpy(batchTensor.data() + idx * Classifier::N,
                    entry.sample.data(),
                    Classifier::N * sizeof(std::complex<float>));
        entry_idx = (entry_idx + 1) % entries.size();
    }
}

void Shutdown() {
    std::queue<std::shared_ptr<Classifier>> empty;
    std::swap(clss, empty);
}

void BM_Preload(benchmark::State& state) {
    for (auto _ : state) {
        Preload();
    }
}

void BM_Shutdown(benchmark::State& state) {
    for (auto _ : state) {
        Shutdown();
    }
}

// Benchmark for max function
static void BM_PredictFunction(benchmark::State& state) {
    auto cls = getClassifier();
    for (auto _ : state) {
        std::vector<std::vector<DL_RESULT>> all_result;
        cls->Predict(entries[0].sample, all_result);
    }

    pushClassifier(cls);
}

// Benchmark for max function
static void BM_BatchPredictFunction(benchmark::State& state) {
    auto cls = getClassifier();
    for (auto _ : state) {
        std::vector<std::vector<DL_RESULT>> all_result;
        cls->BatchPredict(batchTensor, all_result);
    }
    pushClassifier(cls);
}
// Register the function as a benchmark
BENCHMARK(BM_Preload)->Iterations(1)->Unit(benchmark::kMillisecond);
BENCHMARK(BM_PredictFunction)->MinTime(100)->Unit(benchmark::kMillisecond);
BENCHMARK(BM_BatchPredictFunction)->MinTime(100)->Unit(benchmark::kMillisecond);
BENCHMARK(BM_BatchPredictFunction)
    ->Iterations(1000)
    ->Threads(1)
    ->Unit(benchmark::kMillisecond);
BENCHMARK(BM_BatchPredictFunction)
    ->Iterations(1000)
    ->Threads(2)
    ->Unit(benchmark::kMillisecond);
BENCHMARK(BM_BatchPredictFunction)
    ->Iterations(1000)
    ->Threads(4)
    ->Unit(benchmark::kMillisecond);
BENCHMARK(BM_Shutdown)->Iterations(1)->Unit(benchmark::kMillisecond);

// Main function to run the benchmark
BENCHMARK_MAIN();