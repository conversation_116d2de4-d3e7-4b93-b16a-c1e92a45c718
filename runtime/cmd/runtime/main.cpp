#include <filesystem>
#include <iostream>
#include <memory>
#include <string>
#include <vector>

#include "algo.h"
#include "args.h"
#include "config.h"
#include "framework.h"
#include "locked_queue.h"
#include "logger.h"
#include "malloc_type.h"
#include "node_file_writer.h"
#include "node_object_maker.h"
#include "node_package_order_checker.h"
#include "node_package_predict.h"
#include "node_package_prepare.h"
#include "node_package_result.h"
#include "node_package_sorter.h"
#include "node_package_stream.h"
#include "node_packer.h"
#include "node_tcp_sender.h"
#include "node_udp_receiver.h"
#include "node_udp_sender.h"
#include "structs.h"

typedef DataFlow::LockedQueueNoWait Queue;
constexpr int recvBatch    = 4;
constexpr int packageBatch = 1;
constexpr int queueSize    = 100;

int main(int argc, char **argv) {
    init_algo();

    auto [ok, arguments] = ParseArgs(argc, argv);
    if (!ok) {
        return -1;
    }

    if (!arguments.WorkDir.empty()) {
        std::filesystem::current_path(arguments.WorkDir);
    }

    Logger::instance().info("work dir is {}",
                            std::filesystem::current_path().string());
    Logger::instance().info("read config from ./config.yaml");
    auto config = LoadConfig("./config.yaml");

    Logger::instance().start(config.log, "radio_runtime");
    PrintJemallocVersion();

    auto memory = std::make_shared<
        DataFlow::NodeObjectMaker<RawDatagram, recvBatch, Queue>>();
    auto source = std::make_shared<
        DataFlow::NodeUdpReceiver<RawDatagram, recvBatch, Queue>>(
        config.network.recv.addr, config.network.recv.port);
    auto writer = std::make_shared<
        DataFlow::NodeFileWriter<RawDatagram, recvBatch, Queue>>(config.storage,
                                                                 "package");
    auto packer = std::make_shared<
        DataFlow::NodePacker<recvBatch, packageBatch, Queue>>();

    auto checker = std::make_shared<
        DataFlow::NodePackageOrderChecker<packageBatch, Queue>>();

    auto prepare = std::vector<std::shared_ptr<DataFlow::ProcessNode<Queue>>>();
    for (std::size_t i = 0; i < config.framework.prepare; i++) {
        auto prepareNode = std::make_shared<
            DataFlow::NodePackagePrepare<packageBatch, Queue>>();
        prepare.push_back(prepareNode);
    }

    auto predict = std::vector<std::shared_ptr<DataFlow::ProcessNode<Queue>>>();
    for (std::size_t i = 0; i < config.framework.predict; i++) {
        auto predictNode =
            std::make_shared<DataFlow::NodePackagePredict<packageBatch, Queue>>(
                config.classifier);
        predict.push_back(predictNode);
    }

    auto stream = std::vector<std::shared_ptr<DataFlow::ProcessNode<Queue>>>();
    for (std::size_t i = 0; i < config.framework.stream; i++) {
        auto streamNode = std::make_shared<
            DataFlow::NodePackageStream<packageBatch, Queue>>();
        stream.push_back(streamNode);
    }

    auto result =
        std::make_shared<DataFlow::NodePackageResult<packageBatch, Queue>>();

    auto sorter =
        std::make_shared<DataFlow::NodePackageSorter<packageBatch, Queue>>();
    auto sender = std::make_shared<
        DataFlow::NodeTCPSender<ComputePackage, packageBatch, Queue>>(
        config.network.send.addr, config.network.send.port);

    DataFlow::Framwork<Queue> framework;
    framework.Connect(memory, source, 0, 0, "memory->source", queueSize);
    // framework.Connect(source, writer, 0, 0, "source->writer", queueSize);
    framework.Connect(source, packer, 0, 0, "source->packer", queueSize);
    framework.Connect(packer, checker, 0, 0, "packer->checker", queueSize);
    framework.FanOut(checker, prepare, 0, 0, "checker->prepare", queueSize);
    framework.FanToFan(prepare, predict, 0, 0, "prepare->predict", queueSize);
    framework.FanToFan(predict, stream, 0, 0, "predict->stream", queueSize);
    framework.FanIn(stream, result, 0, 0, "stream->result", queueSize);
    framework.Connect(result, sorter, 0, 0, "result->sorter", queueSize);
    framework.Connect(sorter, sender, 0, 0, "sorter->sender", queueSize);

    framework.Start();
    framework.Join();
    return 0;
}