#pragma once

#include <array>
#include <complex>
#include <cstdio>
#include <memory>
#include <string>
#include <vector>

#include <Eigen/Dense>

#include <onnxruntime_cxx_api.h>

#include "config.h"
#include "structs.h"

typedef struct _DL_RESULT {
    std::size_t dimId;
    std::size_t classId;
    float confidence;
} DL_RESULT;


class Classifier {
  public:
    static constexpr std::size_t N = MaxSample;
    static constexpr std::size_t B = MaxWindow;
    typedef sample_t InTensor;
    typedef std::vector<int64_t> InShape;
    Classifier(ClassifierConfig config);
    ~Classifier();

    Classifier() = delete;
    void Predict(InTensor& input, std::vector<std::vector<DL_RESULT>>& result);
    void BatchPredict(InTensor& input,
                      std::vector<std::vector<DL_RESULT>>& result);

  private:
    void CreateSession(std::string onnx_path_string, std::string provider);
    void TensorProcess(InTensor& input,
                       InShape& input_shape,
                       std::vector<std::vector<DL_RESULT>>& results);
    void BatchTensorProcess(InTensor& input,
                            InShape& input_shape,
                            std::vector<std::vector<DL_RESULT>>& results);
    Ort::Env env;
    std::shared_ptr<Ort::Session> session;
    Ort::RunOptions options;

    std::vector<const char*> input_node_names_char;
    std::vector<const char*> output_node_names_char;
    std::vector<std::string> input_node_names;
    std::vector<std::string> output_node_names;
};
