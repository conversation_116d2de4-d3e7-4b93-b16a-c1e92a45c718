#pragma once

#include <array>
#include <chrono>
#include <memory>
#include <queue>
#include <string>
#include <thread>

#include "classifier.h"
#include "config.h"
#include "logger.h"
#include "node_base_batch.h"
#include "package.h"
#include "structs.h"

namespace DataFlow {
template <int B, typename Queue>
class NodePackagePredict : public ProcessNodeBatch<ComputePackage, B, B, Queue> {
  private:
    using Base = ProcessNode<Queue>;
    using BaseBatch = ProcessNodeBatch<ComputePackage, B, B, Queue>;

    constexpr static int batch = B;

  public:
    NodePackagePredict(ClassifierConfig config) : config_(config) {
        Base::_inputQueues.resize(1);
        Base::_outputQueues.resize(1);
    }
    ~NodePackagePredict() = default;

    int Start() {
        classifier_ = std::make_shared<Classifier>(config_);
        thread_     = std::thread(&NodePackagePredict::loop_, this);
        SetThreadName(thread_, "NdPkgPredict");
        return 0;
    }

    void Join() {
        thread_.join();
    }

    void loop_() {
        while (true) {
            auto exit = BaseBatch::popFromBatch(
                [this](std::shared_ptr<ComputePackage> ptr) {
                    std::vector<std::vector<DL_RESULT>> result;
                    classifier_->BatchPredict(ptr->Batch(), result);
                    batch_label_t labels;
                    for (auto [idx, res] :
                         result[0] | std::ranges::views::enumerate) {
                        labels[idx] = std::tuple<std::uint8_t, float>(
                            res.classId, res.confidence);
                    }
                    ptr->SetLabel(labels);

                    if (BaseBatch::pushToBatch(ptr)) {
                        return true;
                    }
                    return false;
                });
            if (exit) {
                break;
            }
        }
    }

  private:
    std::shared_ptr<Classifier> classifier_;
    ClassifierConfig config_;
    std::thread thread_;
};
} // namespace DataFlow
