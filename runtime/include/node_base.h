#pragma once

#include <array>
#include <chrono>
#include <map>
#include <memory>
#include <thread>
#include <vector>

#include "thread_utils.h"

namespace DataFlow {


template <typename Queue>
class ProcessNode {
  public:
    using SPtr      = std::shared_ptr<ProcessNode>;
    using QueueSPtr = std::shared_ptr<Queue>;
    using QueueType = Queue;

    ProcessNode()                              = default;
    ProcessNode(const ProcessNode&)            = delete;
    ProcessNode& operator=(const ProcessNode&) = delete;
    virtual ~ProcessNode()                     = default;

    virtual int Start() = 0;
    virtual void Join() = 0;

    int GetInputCount() const {
        return _inputQueues.size();
    }
    int GetOutputCount() const {
        return _outputQueues.size();
    }
    bool ConnectInput(QueueSPtr input, std::size_t idx) {
        if (idx < 0 || idx >= _inputQueues.size()) {
            return false;
        }
        _inputQueues[idx] = input;
        return true;
    }

    bool ConnectOutput(QueueSPtr output, std::size_t idx) {
        if (idx < 0 || idx >= _outputQueues.size()) {
            return false;
        }
        _outputQueues[idx] = output;
        return true;
    }

    virtual std::map<std::string, std::size_t> Status() {
        return std::map<std::string, std::size_t>{};
    }

  protected:
    std::vector<QueueSPtr> _inputQueues;
    std::vector<QueueSPtr> _outputQueues;
};
} // namespace DataFlow
