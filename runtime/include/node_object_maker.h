#pragma once

#include "logger.h"
#include "node_base.h"

namespace DataFlow {
template <typename T, int B, typename Queue>
class NodeObjectMaker : public ProcessNode<Queue> {
  private:
    using Base = ProcessNode<Queue>;

    constexpr static int batch = B;
    constexpr static int size  = sizeof(std::remove_extent_t<T>) * batch;

  public:
    NodeObjectMaker() {
        Base::_outputQueues.resize(1);
    }
    ~NodeObjectMaker() {
    }

    int Start() {
        thread_ = std::thread(&NodeObjectMaker::loop_, this);
        SetThreadName(thread_, "NdObjectMaker");
        return 0;
    }

    void Join() {
        thread_.join();
    }

  private:
    void loop_() {
        while (true) {
            auto data =
                std::make_shared<std::array<std::shared_ptr<T>, batch>>();
            for (auto i = 0; i < batch; i++) {
                data->at(i) = std::make_shared<T>();
            }

            while (true) {
                auto [ok, exitFlag] = Base::_outputQueues[0]->push(data);
                if (exitFlag) {
                    return;
                }
                if (ok) {
                    break;
                }

                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }
        }
    }

    std::thread thread_;
};
} // namespace DataFlow