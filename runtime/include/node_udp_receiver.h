#pragma once
#include <array>
#include <chrono>
#include <thread>

#include "logger.h"
#include "node_base.h"
#include "socket.h"
#include "thread_utils.h"

namespace DataFlow {
template <typename T, int B, typename Queue>
class NodeUdpReceiver : public ProcessNode<Queue> {
  private:
    using Base                   = ProcessNode<Queue>;
    static constexpr int batch   = B;
    static constexpr int recvLen = sizeof(std::remove_extent_t<T>);

  public:
    // 构造函数
    NodeUdpReceiver(std::string addr, std::uint16_t port)
        : _addr(addr), _port(port) {
        Base::_outputQueues.resize(1);
        Base::_inputQueues.resize(1);
    }

    // 析构函数，用于清理资源，比如关闭套接字等（这里暂未涉及实际关闭操作，可根据需要补充）
    virtual ~NodeUdpReceiver() {
    }

    // 启动UDP接收操作，创建并启动接收线程
    int Start() {
        int err = _server.Listen(_addr, _port);
        if (err != 0) {
            return err;
        }

        thread_ = std::thread(&NodeUdpReceiver::loop_, this);
        SetThreadName(thread_, "NdUdpReceiver");
        return 0;
    }

    // 等待UDP接收线程完成
    void Join() {
        thread_.join();
    }

    std::map<std::string, std::size_t> Status() {
        return std::map<std::string, std::size_t>{};
    }

  private:
    void loop_() {
        while (true) {
            auto [data, exitFlag] =
                Base::_inputQueues[0]
                    ->template pop<std::array<std::shared_ptr<T>, batch>>();
            if (exitFlag) {
                return;
            }
            if (data == nullptr) {
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
                continue;
            }

            for (auto i = 0; i < batch; i++) {
                auto err = _server.Recv(
                    reinterpret_cast<char*>(data.get()->at(i).get()), recvLen);
                if (err != 0) {
                    Logger::instance().error("recv error: {}", err);
                    continue;
                }
            }

            auto [ok, exitPushFlag] = Base::_outputQueues[0]->push(data);
            if (exitPushFlag) {
                break;
            }
        }
    }

    std::string _addr;
    std::uint16_t _port;
    UDPServer _server{};
    std::thread thread_;
};
} // namespace DataFlow