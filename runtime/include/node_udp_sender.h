#pragma once

#include <array>
#include <chrono>
#include <list>
#include <memory>
#include <queue>
#include <thread>
#include <vector>

#include "logger.h"
#include "node_base.h"
#include "package.h"
#include "structs.h"
#include "socket.h"

namespace DataFlow {
template <typename T, int B, typename Queue>
class NodeUDPSender : public ProcessNode<Queue> {
  private:
    using Base                      = ProcessNode<Queue>;
    static constexpr int sendLen    = sizeof(std::remove_extent_t<T>);
    constexpr static int inputBatch = B;

  public:
    NodeUDPSender(std::string addr, std::uint16_t port)
        : _addr(addr), _port(port) {
        Base::_inputQueues.resize(1);
    }
    ~NodeUDPSender() = default;

    int Start() {
        // _client.Connect(_addr, _port);

        thread_ = std::thread(&NodeUDPSender::loop_, this);
        SetThreadName(thread_, "NdSorter");
        return 0;
    }

    void Join() {
        thread_.join();
    }

    std::map<std::string, std::size_t> Status() {
        std::size_t sendSuccess = _sendSuccess;
        std::size_t sendFail    = _sendFail;
        _sendSuccess            = 0;
        _sendFail               = 0;

        return std::map<std::string, std::size_t>{
            {"sender.success", sendSuccess}, {"sender.fail", sendFail}};
    }

  private:
    void loop_() {
        while (true) {
            auto [data, exitFlag] =
                Base::_inputQueues[0]
                    ->template pop<
                        std::array<std::shared_ptr<ComputePackage>, B>>();
            if (exitFlag) {
                break;
            }
            if (data == nullptr) {
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
                continue;
            }

            for (auto i = 0; i < inputBatch; i++) {
                auto ptr = (data.get())->at(i);
                if ((_lastID != 0) && ((_lastID + 1) != ptr->ID())) {
                    Logger::instance().error(
                        "[sender] package id not continuous: {}->{}", _lastID,
                        ptr->ID());
                }
                _lastID = ptr->ID();
                for (auto item : ptr->Data()) {
                    auto err =
                        _client.SendTo(reinterpret_cast<char*>(item.get()),
                                       sendLen, _addr, _port);
                    if (err == success) {
                        _sendSuccess++;
                    } else {
                        _sendFail++;
                    }
                }
            }
        }
    }

    std::size_t _sendSuccess{0};
    std::size_t _sendFail{0};
    std::string _addr;
    std::uint16_t _port{0};
    std::thread thread_;
    std::size_t _lastID{0};
    UDPClient _client;
};
} // namespace DataFlow