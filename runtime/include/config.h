#pragma once

#include <string>
#include <yaml-cpp/yaml.h>

#include "logger_config.h"

// Support at most 4 parameters conversion. The number can be expanded by
// adding: YAML_TO_CLASS_[N](TYPE, NAME, ...) CLASS.NAME =
// node[#NAME].as<TYPE>(); YAML_TO_CLASS_[N-1](__VA_ARGS__)
#define YAML_TO_CLASS_1(CLASS, NAME, ...)                                      \
    rhs.NAME = node[#NAME].as<decltype(CLASS::NAME)>();
#define YAML_TO_CLASS_2(CLASS, NAME, ...)                                      \
    rhs.NAME = node[#NAME].as<decltype(CLASS::NAME)>();                        \
    YAML_TO_CLASS_1(CLASS, __VA_ARGS__)
#define YAML_TO_CLASS_3(CLASS, NAME, ...)                                      \
    rhs.NAME = node[#NAME].as<decltype(CLASS::NAME)>();                        \
    YAML_TO_CLASS_2(CLA<PERSON>, __VA_ARGS__)
#define YAML_TO_CLASS_4(CLASS, NAME, ...)                                      \
    rhs.NAME = node[#NAME].as<decltype(CLASS::NAME)>();                        \
    YAML_TO_CLASS_3(CLASS, __VA_ARGS__)
#define YAML_TO_CLASS_5(CLASS, NAME, ...)                                      \
    rhs.NAME = node[#NAME].as<decltype(CLASS::NAME)>();                        \
    YAML_TO_CLASS_4(CLASS, __VA_ARGS__)

#define CLASS_TO_YAML_1(NAME, ...) node[#NAME] = rhs.NAME;
#define CLASS_TO_YAML_2(NAME, ...)                                             \
    node[#NAME] = rhs.NAME;                                                    \
    CLASS_TO_YAML_1(__VA_ARGS__)
#define CLASS_TO_YAML_3(NAME, ...)                                             \
    node[#NAME] = rhs.NAME;                                                    \
    CLASS_TO_YAML_2(__VA_ARGS__)
#define CLASS_TO_YAML_4(NAME, ...)                                             \
    node[#NAME] = rhs.NAME;                                                    \
    CLASS_TO_YAML_3(__VA_ARGS__)
#define CLASS_TO_YAML_5(NAME, ...)                                             \
    node[#NAME] = rhs.NAME;                                                    \
    CLASS_TO_YAML_4(__VA_ARGS__)

#define YAML_COVERTER(CLASS, NUM, ...)                                         \
    namespace YAML {                                                           \
    template <>                                                                \
    struct convert<CLASS> {                                                    \
        static Node encode(const CLASS& rhs) {                                 \
            Node node;                                                         \
            CLASS_TO_YAML_##NUM(__VA_ARGS__) return node;                      \
        }                                                                      \
                                                                               \
        static bool decode(const Node& node, CLASS& rhs) {                     \
            if (!node.IsMap())                                                 \
                return false;                                                  \
            YAML_TO_CLASS_##NUM(CLASS, __VA_ARGS__) return true;               \
        }                                                                      \
    };                                                                         \
    }

struct AddrConfig {
    std::uint16_t port;
    std::string addr;
};

struct NetworkConfig {
    AddrConfig recv;
    AddrConfig send;
};

struct StorageConfig {
    std::string path;
};

struct FrameWorkConfig {
    std::size_t prepare;
    std::size_t predict;
    std::size_t stream;
};

struct ClassifierConfig {
    std::string model;    // onnx model path
    std::string provider; // "TensorRT" "CUDA" "CPU"
};


struct APPConfig {
    LogConfig log;
    NetworkConfig network;
    StorageConfig storage;
    FrameWorkConfig framework;
    ClassifierConfig classifier;
};

APPConfig LoadConfig(std::string path);



YAML_COVERTER(AddrConfig, 2, port, addr);
YAML_COVERTER(LogConfig, 5, path, level, maxSize, maxFiles, console);
YAML_COVERTER(NetworkConfig, 2, recv, send);
YAML_COVERTER(StorageConfig, 1, path);
YAML_COVERTER(FrameWorkConfig, 3, prepare, predict, stream);
YAML_COVERTER(ClassifierConfig, 2, model, provider);
YAML_COVERTER(APPConfig, 5, log, network, storage, framework, classifier);