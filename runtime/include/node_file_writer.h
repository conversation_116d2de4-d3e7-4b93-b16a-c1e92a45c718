#pragma once

#include <filesystem>
#include <fstream>

#include "logger.h"
#include "node_base_batch.h"
#include "structs_traits.h"

template <typename T>
struct isTextOutput : std::false_type {};

template <typename T>
std::size_t getStructSize(T* ptr) {
    return sizeof(std::remove_extent_t<T>);
}

template <typename T>
std::size_t getStructSize(std::shared_ptr<T>* ptr) {
    return getStructSize(ptr->get());
}

template <typename T>
void fillBuffer(std::shared_ptr<T> ptr, std::uint8_t* buffer) {
    return fillBuffer(ptr.get(), buffer);
}

template <typename T>
void fillBuffer(T* ptr, std::uint8_t* buffer) {
    std::memcpy(buffer, ptr, sizeof(std::remove_extent_t<T>));
}

template <typename T>
void fillStream(std::shared_ptr<T> ptr, std::ofstream& outFile) {
    fillStream(ptr.get(), outFile);
}

template <typename T>
std::size_t getIndex(std::shared_ptr<T>* ptr) {
    return getIndex(ptr->get());
}

namespace fs = std::filesystem;

namespace DataFlow {
template <typename T, int B, typename Queue>
class NodeFileWriter : public ProcessNodeBatch<T, B, B, Queue> {
  private:
    using Base      = ProcessNode<Queue>;
    using BaseBatch = ProcessNodeBatch<T, B, B, Queue>;

    constexpr static int batch = B;

  public:
    NodeFileWriter(StorageConfig config, std::string_view name) {
        _path   = fs::path(config.path).append(name);
        _prefix = name;
        Base::_inputQueues.resize(1);
    }
    ~NodeFileWriter() {
    }

    int Start() {
        thread_ = std::thread(&NodeFileWriter::loop_, this);
        SetThreadName(thread_, "NdFileWriter");
        return 0;
    }

    void Join() {
        thread_.join();
    }

    void loop_() {
        std::unique_ptr<std::uint8_t[]> buffer{};
        std::size_t buffSize{};
        fs::create_directories(_path);

        while (true) {
            auto exit = BaseBatch::popFromBatch([&](std::shared_ptr<T> ptr) {
                std::size_t idx = getIndex(ptr.get());
                namespace cho   = std::chrono;
                auto now        = cho::time_point_cast<cho::seconds>(
                    cho::system_clock::now());

                auto suffix   = isTextOutput<T>::value ? "csv" : "bin";
                auto filename = std::format("{}_{:%Y_%m_%d_%H_%M_%S}_{:03}.{}",
                                            _prefix,
                                            now,
                                            idx,
                                            suffix);

                std::ofstream outFile;
                outFile.open((_path / filename).string(),
                             std::ios::openmode(std::ios::out | std::ios::app));
                if (!outFile.is_open()) {
                    Logger::instance().error("unable to open output file");
                    return false;
                }

                if constexpr (isTextOutput<T>::value) {
                    fillStream(ptr.get(), outFile);
                } else {
                    if (buffer == nullptr) {
                        buffSize = getStructSize(ptr.get());
                        buffer   = std::unique_ptr<std::uint8_t[]>(
                            new std::uint8_t[buffSize]{});
                    }
                    fillBuffer(ptr.get(), buffer.get());
                    outFile.write(reinterpret_cast<char*>(buffer.get()),
                                  buffSize);
                }

                outFile.flush();
                outFile.close();
                return false;
            });
            if (exit) {
                break;
            }
        }
    }

    fs::path _path;
    std::string _prefix;
    std::thread thread_;
};
} // namespace DataFlow