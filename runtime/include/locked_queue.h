#pragma once

#include <any>
#include <atomic>
#include <memory>
#include <mutex>
#include <queue>
#include <tuple>

namespace DataFlow {
template <bool wait = true>
class LockedQueue {
  public:
    using SPtr = std::shared_ptr<LockedQueue>;

    LockedQueue(std::size_t max) : maxSize_(max) {
    }
    LockedQueue() : maxSize_(0) {
    }

    template <typename T>
    std::tuple<bool, bool> push(std::shared_ptr<T> item) {
        if constexpr (wait) {
            return pushWait<T>(item);
        } else {
            return pushNoWait<T>(item);
        }
    }

    template <typename T>
    std::tuple<std::shared_ptr<T>, bool> pop() {
        if constexpr (wait) {
            return popWait<T>();
        } else {
            return popNoWait<T>();
        }
    }

    template <typename T>
    std::tuple<bool, bool> pushWait(std::shared_ptr<T> item) {
        auto flag = exitflag_.load();
        if (flag) {
            pushFail_++;
            return std::make_tuple(false, flag);
        }

        std::unique_lock<std::mutex> lock(queueMutex);
        pushCV.wait(lock, [this] {
            return maxSize_ == 0 || dataQueue.size() < maxSize_ ||
                   exitflag_.load();
        });
        flag = exitflag_.load();
        if (flag) {
            pushFail_++;
            return std::make_tuple(false, flag);
        }

        dataQueue.push(item);
        popCV.notify_one();
        pushSuccess_++;
        return std::make_tuple(true, flag);
    }

    template <typename T>
    std::tuple<std::shared_ptr<T>, bool> popWait() {
        std::shared_ptr<T> item{};
        auto flag = exitflag_.load();
        if (flag) {
            popFail_++;
            return std::make_tuple(item, flag);
        }

        std::unique_lock<std::mutex> lock(queueMutex);
        popCV.wait(lock,
                   [this] { return (exitflag_.load() || !dataQueue.empty()); });
        flag = exitflag_.load();
        if (flag) {
            popFail_++;
            return std::make_tuple(item, flag);
        }

        item = std::any_cast<std::shared_ptr<T>>(dataQueue.front());
        dataQueue.pop();
        pushCV.notify_one();
        popSuccess_++;
        return std::make_tuple(item, flag);
    }

    template <typename T>
    std::tuple<bool, bool> pushNoWait(std::shared_ptr<T> item) {
        auto flag = exitflag_.load();
        if (flag) {
            pushFail_++;
            return std::make_tuple(false, flag);
        }

        std::lock_guard<std::mutex> guard(queueMutex);
        if (maxSize_ > 0 && dataQueue.size() >= maxSize_) {
            pushFail_++;
            return std::make_tuple(false, flag);
        }
        dataQueue.push(item);
        pushSuccess_++;
        return std::make_tuple(true, flag);
    }

    template <typename T>
    std::tuple<std::shared_ptr<T>, bool> popNoWait() {
        std::shared_ptr<T> item{};
        auto flag = exitflag_.load();
        if (flag) {
            popFail_++;
            return std::make_tuple(item, flag);
        }

        std::unique_lock<std::mutex> lock(queueMutex);
        if (dataQueue.empty()) {
            popFail_++;
            return std::make_tuple(item, flag);
        }
        item = std::any_cast<std::shared_ptr<T>>(dataQueue.front());
        dataQueue.pop();
        popSuccess_++;
        return std::make_tuple(item, flag);
    }

    inline void close() {
        exitflag_.store(true);
        std::unique_lock<std::mutex> lock(queueMutex);
        popCV.notify_all();
        pushCV.notify_all();
    }
    inline std::tuple<std::size_t, bool> size() {
        std::unique_lock<std::mutex> lock(queueMutex);
        return std::make_tuple(dataQueue.size(), exitflag_.load());
    }

    std::map<std::string, std::size_t> status() {
        auto size        = dataQueue.size();
        auto pushSuccess = pushSuccess_;
        auto pushFail    = pushFail_;
        auto popSuccess  = popSuccess_;
        auto popFail     = popFail_;

        pushSuccess_ = 0;
        pushFail_    = 0;
        popSuccess_  = 0;
        popFail_     = 0;

        return std::map<std::string, std::size_t>{
            {"push.success", pushSuccess},
            {"push.fail", pushFail},
            {"size", size},
            {"pop.success", popSuccess},
            {"pop.fail", popFail},
        };
    }

  private:
    std::size_t pushSuccess_{0};
    std::size_t pushFail_{0};
    std::size_t popSuccess_{0};
    std::size_t popFail_{0};

    std::atomic<bool> exitflag_{false};
    std::queue<std::any> dataQueue;
    std::size_t maxSize_;
    std::mutex queueMutex;
    std::condition_variable popCV;
    std::condition_variable pushCV;
};

typedef LockedQueue<false> LockedQueueNoWait;
typedef LockedQueue<true> LockedQueueWait;
} // namespace DataFlow