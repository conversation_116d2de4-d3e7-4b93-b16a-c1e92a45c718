#pragma once
#include <Eigen/Dense>
#include <complex>
#include <expected>
#include <numeric>

#include "structs.h"

inline std::array<float, MaxSample> _hanning_window;

inline void init_algo() {
    for (std::size_t i = 0; i < MaxSample; i++) {
        _hanning_window[i] =
            0.5f * (1.0f - std::cos(2.0f * M_PI * i / (MaxSample - 1)));
    }
}

template <int N>
void softmax(float* data) {
    float max_val = data[0];
    for (int i = 1; i < N; ++i) {
        if (data[i] > max_val) {
            max_val = data[i];
        }
    }

    float sum_exp = 0.0f;
    for (int i = 0; i < N; ++i) {
        data[i] = std::exp(data[i] - max_val);
        sum_exp += data[i];
    }

    for (int i = 0; i < N; ++i) {
        data[i] /= sum_exp;
    }
}

template <int B, int N>
void iq_to_amp_phs(sample_t& data) {
    for (int row = 0; row < data.rows(); ++row) {
        for (int col = 0; col < data.cols(); ++col) {
            float abs_val  = std::abs(data(row, col));
            float arg_val  = std::arg(data(row, col)) / M_PI;
            data(row, col) = std::complex<float>(abs_val, arg_val);
        }
    }
}

template <int B, int N>
void medfilt(sample_t& data, int kernel_size) {
    if (kernel_size % 2 == 0 || kernel_size < 1) {
        throw std::invalid_argument(
            "Kernel size must be a positive odd number.");
    }

    sample_t temp = data;
    int half_k    = kernel_size / 2;

    for (int i = 0; i < data.rows(); ++i) {

        Eigen::VectorXf window_real(kernel_size);
        Eigen::VectorXf window_imag(kernel_size);

        for (int j = 0; j < data.cols(); ++j) {
            int k = 0;
            for (int n = -half_k; n <= half_k; ++n) {
                int col_idx    = std::clamp(j + n, 0, N - 1);
                window_real(k) = temp(i, col_idx).real();
                window_imag(k) = temp(i, col_idx).imag();
                k++;
            }
            std::nth_element(window_real.data(), window_real.data() + half_k,
                             window_real.data() + window_real.size());
            std::nth_element(window_imag.data(), window_imag.data() + half_k,
                             window_imag.data() + window_imag.size());
            data(i, j) =
                std::complex<float>(window_real(half_k), window_imag(half_k));
        }
    }
}

template <int B, int N>
void normalize_amp(sample_t& data) {
    for (int row = 0; row < data.rows(); ++row) {
        float sum_squares = 0.0f;
        for (int col = 0; col < data.cols(); ++col) {
            sum_squares += std::norm(data(row, col).real());
        }

        float norm_factor = std::sqrt(sum_squares);
        for (int col = 0; col < data.cols(); ++col) {
            data(row, col) = std::complex<float>(
                data(row, col).real() / norm_factor, data(row, col).imag());
        }
    }
}

template <int B, int N>
void normalize_phs(sample_t& data) {
    for (int row = 0; row < data.rows(); ++row) {
        float min_phs = data(row, 0).imag();
        float max_phs = data(row, 0).imag();

        for (int col = 0; col < data.cols(); ++col) {
            float phs = data(row, col).imag();
            if (phs < min_phs)
                min_phs = phs;
            if (phs > max_phs)
                max_phs = phs;
        }

        float k = 2.0f / (max_phs - min_phs);

        for (int col = 0; col < data.cols(); ++col) {
            float phs      = data(row, col).imag();
            data(row, col) = std::complex<float>(data(row, col).real(),
                                                 -1.0f + k * (phs - min_phs));
        }
    }
}

template <int B, int N>
void prepare_data(sample_t& input) {
    medfilt<B, N>(input, 5);
    iq_to_amp_phs<B, N>(input);
    normalize_amp<B, N>(input);
    normalize_phs<B, N>(input);
}

template <std::size_t N>
void apply_hanning_window(std::array<std::complex<float>, N>& input) {
    for (std::size_t i = 0; i < N; i++) {
        input[i] = input[i] * _hanning_window[i];
    }
}

// Function to perform fftshift on a 1D array of complex numbers
template <typename T, std::size_t N>
void fftshift(std::array<std::complex<T>, N>& data) {
    auto mid = N / 2;
    std::rotate(data.begin(), data.begin() + mid, data.end());
}

template <std::size_t N>
void fftfreq(std::size_t sampleRate, std::array<float, N>& freq_axis) {
    // Calculate frequency axis equivalent to Python's np.fft.fftfreq
    // and fftshift
    float freq_step = (float)(sampleRate) / (float)(N);
    for (std::size_t i = 0; i < N; i++) {
        freq_axis[i] = (i < N / 2) ? (float)(i) * freq_step : freq_step * ((float)(i) - N);
    }
    // Perform fftshift
    std::rotate(freq_axis.begin(), freq_axis.begin() + N / 2, freq_axis.end());
}

template <std::size_t N>
std::size_t find_peak_index(const std::array<float, N>& power) {
    std::size_t peak_index = 0;
    float max_value        = power[0];
    for (std::size_t i = 1; i < N; ++i) {
        if (power[i] > max_value) {
            max_value  = power[i];
            peak_index = i;
        }
    }
    return peak_index;
}

template <std::size_t N>
auto calculate_occupied_bandwidth(std::array<float, N>& power) {
    float reference_power = std::accumulate(power.begin(), power.end(), 0.0f);
    float threshold       = 0.005f * reference_power;

    // Find lower frequency bound
    float cumulative_power_low   = 0.0f;
    size_t first_frequency_index = 0;
    for (size_t i = 0; i < MaxSample; ++i) {
        cumulative_power_low += power[i];
        if (cumulative_power_low >= threshold) {
            first_frequency_index = i;
            break;
        }
    }

    // Find upper frequency bound
    float cumulative_power_high   = 0.0f;
    size_t second_frequency_index = MaxSample - 1;
    for (size_t i = MaxSample - 1; i != size_t(-1); --i) {
        cumulative_power_high += power[i];
        if (cumulative_power_high >= threshold) {
            second_frequency_index = i;
            break;
        }
    }

    return std::pair<std::size_t, std::size_t>(first_frequency_index,
                                               second_frequency_index);
}