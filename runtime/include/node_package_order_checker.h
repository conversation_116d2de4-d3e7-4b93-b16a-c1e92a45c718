#pragma once

#include <array>
#include <chrono>
#include <memory>
#include <queue>
#include <thread>

#include "logger.h"
#include "node_base_batch.h"
#include "package.h"
#include "structs.h"

namespace DataFlow {
template <int B, typename Queue>
class NodePackageOrderChecker : public ProcessNodeBatch<ComputePackage, B, B, Queue> {
  private:
    using Base = ProcessNode<Queue>;
    using BaseBatch = ProcessNodeBatch<ComputePackage, B, B, Queue>;

    constexpr static int batch = B;

  public:
    NodePackageOrderChecker() {
        Base::_inputQueues.resize(1);
        Base::_outputQueues.resize(1);
    }
    ~NodePackageOrderChecker() = default;

    int Start() {
        thread_ = std::thread(&NodePackageOrderChecker::loop_, this);
        SetThreadName(thread_, "NdPkgOdrChk");
        return 0;
    }

    void Join() {
        thread_.join();
    }

    void loop_() {
        while (true) {
            auto exit = BaseBatch::popFromBatch(
                [this](std::shared_ptr<ComputePackage> ptr) {
                    if ((_lastID != 0) && ((_lastID + 1) != ptr->ID())) {
                        Logger::instance().error(
                            "[checker] package id not continuous: {}->{}",
                            _lastID, ptr->ID());
                    }
                    _lastID = ptr->ID();

                    if (BaseBatch::pushToBatch(ptr)) {
                        return true;
                    }
                    return false;
                });
            if (exit) {
                break;
            }
        }
    }

  private:
    std::thread thread_;
    std::size_t _lastID{0};
};
} // namespace DataFlow