#pragma once

#include <string>
#include <format>

#include <jemalloc/jemalloc.h>
#include "logger.h"
static void PrintJemallocVersion() {
    const char *version;
    std::size_t version_len = sizeof(version);
    if (mallctl("version", &version, &version_len, NULL, 0) == 0) {
        Logger::instance().info("jemalloc version: {}", version);
    } else {
        Logger::instance().error("get jemalloc version fail");
    }
}