#pragma once

#include <cstdint>
#include <filesystem>
#include <memory>
#include <string>

#include <spdlog/sinks/rotating_file_sink.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/spdlog.h>
#include <spdlog/common.h>

#include "singleton.h"
#include "logger_config.h"

namespace fs = std::filesystem;

class Logger : public Singleton<Logger> {
    friend class Singleton<Logger>;
    typedef std::shared_ptr<spdlog::logger> logPtr;

  private:
    logPtr _ptr{spdlog::default_logger()};

  public:
    Logger() = default;

    void start(LogConfig &cfg, std::string modulename) {
        auto levelint = spdlog::level::from_str(cfg.level);

        fs::path path(cfg.path);
        fs::create_directories(path);
        auto filepath = path / (modulename + ".log");

        auto conSink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        conSink->set_level(levelint);
        conSink->set_pattern(std::string("[%Y-%m-%d %H:%M:%S][%^%l%$] %v"));
        auto fileSink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
            filepath.string(), cfg.maxSize, cfg.maxFiles);

        _ptr = logPtr(new spdlog::logger("multi_sink", {conSink, fileSink}));
        _ptr->set_level(levelint);
    }

    template <typename... Args>
    void trace(spdlog::format_string_t<Args...> fmt, Args &&...args) {
        _ptr->trace(fmt, std::forward<Args>(args)...);
    }

    template <typename... Args>
    void debug(spdlog::format_string_t<Args...> fmt, Args &&...args) {
        _ptr->debug(fmt, std::forward<Args>(args)...);
    }

    template <typename... Args>
    void info(spdlog::format_string_t<Args...> fmt, Args &&...args) {
        _ptr->info(fmt, std::forward<Args>(args)...);
    }

    template <typename... Args>
    void warn(spdlog::format_string_t<Args...> fmt, Args &&...args) {
        _ptr->warn(fmt, std::forward<Args>(args)...);
    }

    template <typename... Args>
    void error(spdlog::format_string_t<Args...> fmt, Args &&...args) {
        _ptr->error(fmt, std::forward<Args>(args)...);
    }

    template <typename... Args>
    void critical(spdlog::format_string_t<Args...> fmt, Args &&...args) {
        _ptr->critical(fmt, std::forward<Args>(args)...);
    }

    template <typename T>
    void trace(const T &msg) {
        _ptr->trace(msg);
    }

    template <typename T>
    void debug(const T &msg) {
        _ptr->debug(msg);
    }

    template <typename T>
    void info(const T &msg) {
        _ptr->info(msg);
    }

    template <typename T>
    void warn(const T &msg) {
        _ptr->warn(msg);
    }

    template <typename T>
    void error(const T &msg) {
        _ptr->error(msg);
    }

    template <typename T>
    void critical(const T &msg) {
        _ptr->critical(msg);
    }

};
