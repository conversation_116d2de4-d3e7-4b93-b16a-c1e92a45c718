#pragma once
#include <arpa/inet.h> // This contains inet_addr
#include <sys/socket.h>
#include <unistd.h> // This contains close

#include <cstdint>
#include <stdexcept>

#define INVALID_SOCKET (SOCKET)(~0)
#define SOCKET_ERROR (-1)
typedef int SOCKET;

namespace DataFlow {

const int success                = 0;
const int socket_bind_err        = 3;
const int socket_accept_err      = 4;
const int connect_err            = 5;
const int message_send_err       = 6;
const int receive_err            = 7;
const int receive_not_enough_err = 8;
const int send_err               = 9;
const int send_not_enough_err    = 10;

// These could also be enums

class Socket {
  public:
    enum class SocketType {
        TYPE_STREAM = SOCK_STREAM,
        TYPE_DGRAM  = SOCK_DGRAM
    };

    explicit Socket(SocketType socket_type);
    ~Socket();
    void setPort(std::uint16_t port);
    int setAddress(const std::string& address);
    void setOpt();
    int bind();
    int connect();
    SOCKET get() const;
    SOCKET m_socket;
    sockaddr_in m_addr;
};

class UDPServer {
  public:
    UDPServer()  = default;
    ~UDPServer() = default;

    int Listen(const std::string& address = "0.0.0.0",
               std::uint16_t port         = 8000);
    int Recv(char* buffer, int len);

  private:
    Socket listenSocket_{Socket::SocketType::TYPE_DGRAM};
};

class UDPClient {
  public:
    UDPClient()  = default;
    ~UDPClient() = default;

    int Connect(const std::string& address, std::uint16_t port);
    int Send(const char* buffer, int len);
    int SendTo(const char* buffer,
               int len,
               std::string addr,
               std::uint16_t port);

  private:
    Socket clientSocket_{Socket::SocketType::TYPE_DGRAM};
};

class TCPClient {
  public:
    TCPClient()  = default;
    ~TCPClient() = default;

    int Connect(const std::string& address, std::uint16_t port);
    int Send(const char* buffer, std::size_t len);
    int Recv(char* buffer, std::size_t len);

  private:
    Socket clientSocket_{Socket::SocketType::TYPE_STREAM};
};

} // namespace DataFlow