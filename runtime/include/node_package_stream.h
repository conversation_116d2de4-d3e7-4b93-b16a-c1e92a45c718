#pragma once

#include <array>
#include <chrono>
#include <list>
#include <memory>
#include <queue>
#include <thread>
#include <vector>

#include "logger.h"
#include "node_base_batch.h"
#include "package.h"
#include "structs.h"

namespace DataFlow {
template <int B, typename Queue>
class NodePackageStream : public ProcessNodeBatch<ComputePackage, B, B, Queue> {
  private:
    using Base = ProcessNode<Queue>;
    using BaseBatch = ProcessNodeBatch<ComputePackage, B, B, Queue>;

    constexpr static int inputBatch  = B;
    constexpr static int outputBatch = B;

  public:
    NodePackageStream() {
        Base::_inputQueues.resize(1);
        Base::_outputQueues.resize(1);
    }
    ~NodePackageStream() = default;

    int Start() {
        thread_ = std::thread(&NodePackageStream::loop_, this);
        SetThreadName(thread_, "NdPkgStream");
        return 0;
    }

    void Join() {
        thread_.join();
    }

    void loop_() {
        while (true) {
            auto exit = BaseBatch::popFromBatch(
                [this](std::shared_ptr<ComputePackage> ptr) {
                    ptr->PrepareStream();
                    if (BaseBatch::pushToBatch(ptr)) {
                        return true;
                    }
                    return false;
                });
            if (exit) {
                break;
            }
        }
    }

    std::thread thread_;
};
} // namespace DataFlow