#pragma once

#include <array>
#include <chrono>
#include <list>
#include <memory>
#include <queue>
#include <thread>
#include <vector>

#include "logger.h"
#include "node_base_batch.h"
#include "package.h"
#include "structs.h"

namespace DataFlow {
template <int B, typename Queue>
class NodePackageSorter : public ProcessNodeBatch<ComputePackage, B, B, Queue> {
  private:
    using Base      = ProcessNode<Queue>;
    using BaseBatch = ProcessNodeBatch<ComputePackage, B, B, Queue>;

    constexpr static int inputBatch  = B;
    constexpr static int outputBatch = B;
    constexpr static int cacheSize   = 20;
    std::map<std::size_t, std::shared_ptr<ComputePackage>> cache;
    std::size_t lastID = 0;

  public:
    NodePackageSorter() {
        Base::_inputQueues.resize(1);
        Base::_outputQueues.resize(1);
    }
    ~NodePackageSorter() = default;

    int Start() {
        thread_ = std::thread(&NodePackageSorter::loop_, this);
        SetThreadName(thread_, "NdPkgSorter");
        return 0;
    }

    void Join() {
        thread_.join();
    }

    void loop_() {
        while (true) {
            auto exit = BaseBatch::popFromBatch(
                [&](std::shared_ptr<ComputePackage> ptr) {
                    cache[ptr->ID()] = ptr;

                    if (cache.size() >= cacheSize ||
                        (cache.size() > 0 &&
                         ((lastID + 1) == cache.begin()->first))) {
                        lastID = cache.begin()->first;
                        ptr    = cache.begin()->second;
                        cache.erase(cache.begin());
                        if (BaseBatch::pushToBatch(ptr)) {
                            return true;
                        }
                    }

                    return false;
                });
            if (exit) {
                break;
            }
        }
    }

    std::thread thread_;
};
} // namespace DataFlow