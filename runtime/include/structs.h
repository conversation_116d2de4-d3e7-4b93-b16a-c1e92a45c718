#pragma once

#include <array>
#include <bitset>
#include <complex>
#include <ranges>
#include <string>
#include <memory>
#include <stdfloat>
#include <Eigen/Dense>

#include "logger.h"


constexpr std::size_t MaxWindow       = 1024;
constexpr std::size_t MaxSample       = 1024;
constexpr std::size_t TelegramDataLen = 1024;

typedef Eigen::Matrix<std::complex<float>, -1,-1,Eigen::AutoAlign|Eigen::RowMajor> sample_t;



typedef std::array<std::tuple<std::uint8_t, float>, MaxWindow>
    batch_label_t;
typedef struct _RADIO_ENTRY {
    std::string modulation;
    std::size_t label;
    int snr;
    std::size_t rate;
    sample_t sample{1, MaxSample};
} RADIO_ENTRY;

#pragma pack(push, 1)
struct WinHeader {
    std::uint8_t Magic;
    std::uint8_t Type;
    std::uint16_t WinNum;
    std::uint8_t PartNum;
    std::uint8_t Modulation;
    std::uint8_t Confidence;
    std::uint8_t LevelThreshold;
    std::float32_t CarrierFreq;  // 8 GHz carrier frequency
    std::float32_t SampleRate;   // 50 MHz sampling frequency
};

struct DownStreamHeader {
    std::uint8_t Magic{0x5c};
    std::uint8_t Type;
    std::uint16_t WinNum;
    std::uint8_t Modulation;
    std::uint8_t Confidence;
    std::uint8_t LevelThreshold;
    std::uint8_t Reserved1{0};
    std::uint32_t BodySize{0};
    std::float32_t PowerLevelMax{0};
    std::float32_t PowerLevelMin{0};
    std::float32_t PowerLevelAvg{0};
    std::float32_t DominantFreq{0};
    std::float32_t BandWidth{0};
    std::float32_t FreqOffset{0};
    std::float32_t PhaseOffset{0};
};
#pragma pack(pop)

typedef std::array<DownStreamHeader, MaxWindow> batch_dsheader_t;



constexpr std::size_t TelegramLen     = TelegramDataLen + sizeof(WinHeader);
constexpr std::size_t MaxIQDataLen    = TelegramDataLen / sizeof(std::int16_t);
constexpr std::size_t MaxPart         = 4;
constexpr std::size_t MaxComputeParts = MaxWindow * MaxPart;


struct WinData {
    WinHeader Header;
    std::int16_t IQData[MaxIQDataLen];

    bool isValid() {
        return Header.Magic == 0x5b && (Header.Type == 0x01 || Header.Type == 0x02);
    }

    std::size_t index() {
        return static_cast<std::size_t>(Header.WinNum * MaxPart +
                                        Header.PartNum);
    }
    std::size_t window() {
        return static_cast<std::size_t>(Header.WinNum);
    }
    std::size_t part() {
        return static_cast<std::size_t>(Header.PartNum);
    }
};

typedef union {
    WinData Win;
    std::uint8_t RawData[TelegramLen];
} RawDatagram;
