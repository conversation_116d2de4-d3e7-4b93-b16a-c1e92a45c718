#pragma once

#include <array>
#include <chrono>
#include <list>
#include <memory>
#include <queue>
#include <thread>
#include <vector>

#include "logger.h"
#include "node_base.h"
#include "package.h"
#include "socket.h"
#include "structs.h"

namespace DataFlow {
template <typename T, int B, typename Queue>
class NodeTCPSender : public ProcessNode<Queue> {
  private:
    using Base                      = ProcessNode<Queue>;
    constexpr static int inputBatch = B;

  public:
    NodeTCPSender(std::string addr, std::uint16_t port)
        : _addr(addr), _port(port) {
        Base::_inputQueues.resize(1);
    }
    ~NodeTCPSender() = default;

    int Start() {
        thread_ = std::thread(&NodeTCPSender::loop_, this);
        SetThreadName(thread_, "NdTCPSendLp");

        connThread_ = std::thread(&NodeTCPSender::conn_, this);
        SetThreadName(connThread_, "NdTCPSendConn");
        return 0;
    }

    void Join() {
        thread_.join();
        connThread_.join();
    }

    std::map<std::string, std::size_t> Status() {
        std::size_t sendSuccess = _sendSuccess;
        std::size_t sendFail    = _sendFail;
        _sendSuccess            = 0;
        _sendFail               = 0;

        return std::map<std::string, std::size_t>{
            {"sender.success", sendSuccess}, {"sender.fail", sendFail}};
    }

  private:


  void loop_() {
        while (true) {
            auto [data, exitFlag] =
                Base::_inputQueues[0]
                    ->template pop<std::array<std::shared_ptr<T>, B>>();
            if (exitFlag) {
                break;
            }
            if (data == nullptr) {
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
                continue;
            }

            if (_connected.load() == false) {
                for (auto i = 0; i < inputBatch; i++) {
                    auto ptr = (data.get())->at(i);
                    for (auto _ : ptr->RangeStream()) {
                        _sendFail++;
                    }
                }

                std::this_thread::sleep_for(std::chrono::milliseconds(1));
                continue;
            }

            for (auto i = 0; i < inputBatch; i++) {
                auto ptr = (data.get())->at(i);
                if ((_lastID != 0) && ((_lastID + 1) != ptr->ID())) {
                    Logger::instance().error(
                        "[sender] package id not continuous: {}->{}", _lastID,
                        ptr->ID());
                }
                _lastID = ptr->ID();

                for (auto [payload, len] : ptr->RangeStream()) {
                    if (_connected.load() == false) {
                        _sendFail++;
                        continue;
                    }

                    auto err = _client->Send(payload, len);
                    if (err == success) {
                        _sendSuccess++;
                    } else {
                        _sendFail++;
                        _connected.store(false);
                    }
                }
            }
        }
    }

    void conn_() {
        while (true) {
            if (_connected.load()) {
                std::this_thread::sleep_for(std::chrono::milliseconds(1000));
                continue;
            }

            _client  = std::make_shared<TCPClient>();
            auto err = _client->Connect(_addr, _port);
            if (err == success) {
                _connected.store(true);
            } else {
                _client.reset();
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        }
    }

    std::size_t _sendSuccess{0};
    std::size_t _sendFail{0};
    std::string _addr;
    std::uint16_t _port{0};
    std::thread thread_;
    std::thread connThread_;
    std::size_t _lastID{0};
    std::atomic_bool _connected{false};
    std::shared_ptr<TCPClient> _client; // Use TCP client instead of UDP client
};
} // namespace DataFlow
