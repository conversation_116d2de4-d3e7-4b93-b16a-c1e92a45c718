#pragma once

#include <array>
#include <bitset>
#include <cmath>
#include <complex>
#include <iterator>
#include <kissfft/kissfft.hh>
#include <limits>
#include <memory>
#include <ranges>
#include <vector>

#include "algo.h"
#include "structs.h"

// 自定义迭代器类
template <typename T1, typename T2, std::size_t N>
class AlternatingIterator {
  private:
    constexpr static std::size_t limit = N - 1;

    const T1& _data;
    const T2& _dsheader;
    std::size_t _dataIdx;
    std::size_t _headerIdx;
    bool _useHeader;
    std::size_t _counter;

  public:
    AlternatingIterator(const T1& a1, const T2& a2, std::size_t idx1 = 0,
                        std::size_t idx2 = 0, bool use = true, int cnt = 0)
        : _data(a1), _dsheader(a2), _dataIdx(idx1), _headerIdx(idx2),
          _useHeader(use), _counter(cnt) {
    }

    // 前置自增运算符
    AlternatingIterator& operator++() {
        if (_useHeader) {
            if (_headerIdx < _dsheader.size()) {
                ++_headerIdx;
            }
            _useHeader = false;
            _counter   = 0;
        } else {
            if (_counter < limit && _dataIdx < _data.size()) {
                ++_dataIdx;
                ++_counter;
            } else {
                if (_dataIdx < _data.size()) {
                    ++_dataIdx;
                }
                _useHeader = true;
                _counter   = 0;
            }
        }
        return *this;
    }

    // 解引用运算符
    std::pair<const char*, size_t> operator*() const {
        if (_useHeader) {
            const auto& header = _dsheader[_headerIdx];
            return {reinterpret_cast<const char*>(&header),
                    sizeof(DownStreamHeader)};
        } else {
            const auto& datagram = _data[_dataIdx];
            return {reinterpret_cast<const char*>(datagram.get()) +
                        sizeof(WinHeader),
                    TelegramDataLen};
        }
    }

    // 不等于运算符
    bool operator!=(const AlternatingIterator& other) const {
        return _headerIdx != other._headerIdx || _dataIdx != other._dataIdx;
    }
};

class ComputePackage {
    std::size_t _id{};
    std::size_t _start{};
    std::bitset<MaxComputeParts> _parts{};
    std::array<std::shared_ptr<RawDatagram>, MaxComputeParts> _data;
    sample_t _batch{MaxWindow, MaxSample};
    sample_t _rawData{MaxWindow, MaxSample};
    batch_label_t _label;
    batch_dsheader_t _dsheader;

    // 辅助类，用于提供 begin 和 end 方法
    class RangeProxy {
      private:
        const std::array<std::shared_ptr<RawDatagram>, MaxComputeParts>& _data;
        const batch_dsheader_t& _dsheader;

      public:
        RangeProxy(const std::array<std::shared_ptr<RawDatagram>,
                                    MaxComputeParts>& data,
                   const batch_dsheader_t& dsheader)
            : _data(data), _dsheader(dsheader) {
        }

        auto begin() const {
            return AlternatingIterator<
                std::array<std::shared_ptr<RawDatagram>, MaxComputeParts>,
                batch_dsheader_t, MaxPart>(_data, _dsheader);
        }

        auto end() const {
            return AlternatingIterator<
                std::array<std::shared_ptr<RawDatagram>, MaxComputeParts>,
                batch_dsheader_t, MaxPart>(_data, _dsheader, _data.size(),
                                           _dsheader.size(), false, 0);
        }
    };

  public:
    ComputePackage(std::size_t id, std::size_t start) : _id(id), _start(start) {
    }
    ~ComputePackage()                                = default;
    ComputePackage(const ComputePackage&)            = delete;
    ComputePackage& operator=(const ComputePackage&) = delete;

    std::size_t Start() {
        return _start;
    }

    std::size_t ID() {
        return _id;
    }
    std::tuple<bool, bool> FillData(std::shared_ptr<RawDatagram> input) {
        auto diff = (input.get()->Win.index() - _start);
        if (diff >= MaxComputeParts) {
            return std::tuple<bool, bool>{false, true};
        } else if (diff < 0) {
            return std::tuple<bool, bool>{false, false};
        }

        auto idx = input.get()->Win.index() - _start;
        _parts.set(idx);
        _data[idx] = input;
        return std::tuple<bool, bool>{true, false};
    }

    bool IsCompleted() {
        if (_parts.all()) {
            return true;
        }
        return false;
    }

    sample_t& Batch() {
        return this->_batch;
    }

    auto Prepare() {
        for (auto [idx, d] : this->_data | std::views::enumerate) {
            auto iq            = d.get()->Win.IQData;
            auto maxComplexLen = MaxIQDataLen / 2;
            for (std::size_t i = 0; i < maxComplexLen; i++) {
                _batch(idx * maxComplexLen + i) =
                    std::complex<float>(iq[i * 2], iq[i * 2 + 1]);
            }
        }
        _rawData = _batch;
        prepare_data<MaxWindow, MaxSample>(this->_batch);
    }

    auto RangeStream() {
        return RangeProxy(_data, _dsheader);
    }

    auto PrepareStream() {
        for (auto [idx, d] : this->_label | std::views::enumerate) {
            auto [predict, confidence] = d;
            auto winNum = this->_data[idx * MaxPart].get()->Win.Header.WinNum;
            auto type   = this->_data[idx * MaxPart].get()->Win.Header.Type;
            auto threshold = (std::float32_t) this->_data[idx * MaxPart]
                                 .get()
                                 ->Win.Header.LevelThreshold;
            std::float32_t sampleRate =
                this->_data[idx * MaxPart].get()->Win.Header.SampleRate;
            std::float32_t carrierFreq =
                this->_data[idx * MaxPart].get()->Win.Header.CarrierFreq;

            if (type == 2) {
                threshold = 20;
            }

            this->_dsheader[idx].Type       = type;
            this->_dsheader[idx].WinNum     = winNum;
            this->_dsheader[idx].Modulation = predict;
            this->_dsheader[idx].Confidence = std::uint8_t(confidence * 100);
            this->_dsheader[idx].LevelThreshold = threshold;
            this->_dsheader[idx].BodySize       = TelegramDataLen * MaxPart;

            // 计算 电平最大值、最小值、平均值
            auto maxPower = std::numeric_limits<std::float32_t>::min();
            auto minPower = std::numeric_limits<std::float32_t>::max();
            std::float32_t totalPower = 0.0f;
            for (int i = 0; i < _rawData.cols(); i++) {
                auto value = std::abs(_rawData(idx, i));

                totalPower += value;
                if (value > maxPower) {
                    maxPower = value;
                }
                if (value < minPower) {
                    minPower = value;
                }
            }

            // 计算 频偏 以及 中心频率
            std::array<std::complex<float>, MaxSample> signalWindowed{};
            std::memcpy(signalWindowed.data(), &_rawData(idx, 0),
                        this->_rawData.cols() * sizeof(std::complex<float>));
            apply_hanning_window<MaxSample>(signalWindowed);
            std::array<std::complex<float>, MaxSample> fft_result{};
            kissfft<float> fft(MaxSample, false);
            fft.transform(signalWindowed.data(), fft_result.data());
            fftshift(fft_result);

            std::array<float, MaxSample> fft_freq{};
            fftfreq<MaxSample>(sampleRate, fft_freq);
            std::array<float, MaxSample> power{};
            for (std::size_t i = 0; i < MaxSample; ++i) {
                power[i] = std::norm(
                    fft_result[i]); // Equivalent to abs(fft_result)**2
            }

            auto maxIdx     = find_peak_index(power);
            auto freqOffset = fft_freq[maxIdx];

            // 三次样条插值修正
            if (maxIdx > 0 && maxIdx < MaxSample - 1) {
                auto p1          = power[maxIdx - 1];
                auto p2          = power[maxIdx];
                auto p3          = power[maxIdx + 1];
                auto denominator = 2 * p2 - p1 - p3;
                if (std::abs(denominator) > 1e-10) { // 避免除零
                    auto delta_k = (p1 - p3) / denominator;
                    // 限制修正量
                    if (std::abs(delta_k) < 1.0) {
                        freqOffset =
                            freqOffset + delta_k * (sampleRate / MaxSample);
                    }
                }
            }
            std::float32_t dominantFreq = carrierFreq + freqOffset;

            // 计算 带宽
            auto [lower, upper] = calculate_occupied_bandwidth(power);
            auto occupied_bandwidth =
                std::abs(fft_freq[upper] - fft_freq[lower]);

            // 计算 相偏
            std::float32_t phaseOffset = 0.0f;
            std::float32_t phaseRef    = M_PI; // 参考相位设为 π
            std::float32_t pi          = M_PI;
            for (int i = 0; i < this->_rawData.cols(); i++) {
                phaseOffset += std::arg(_rawData(idx, i));
            }
            phaseOffset /= this->_rawData.cols();
            phaseOffset = std::fmod(phaseOffset - phaseRef, 2 * pi);
            if (phaseOffset > pi) {
                phaseOffset -= 2 * pi;
            } else if (phaseOffset < -pi) {
                phaseOffset += 2 * pi;
            }
            auto avgPower = totalPower / this->_rawData.cols();
            this->_dsheader[idx].PowerLevelMax = maxPower;
            this->_dsheader[idx].PowerLevelMin = minPower;
            this->_dsheader[idx].PowerLevelAvg = avgPower;
            this->_dsheader[idx].DominantFreq  = dominantFreq;
            this->_dsheader[idx].FreqOffset    = freqOffset;
            this->_dsheader[idx].PhaseOffset   = phaseOffset;
            this->_dsheader[idx].BandWidth     = occupied_bandwidth;
        }
    }

    auto PrepareLabel() {
        std::size_t match_count = 0;
        for (auto [idx, d] : this->_label | std::views::enumerate) {
            auto [predict, confidence] = d;
            auto label =
                this->_data[idx * MaxPart].get()->Win.Header.Modulation;
            if (label == predict) {
                match_count++;
            }
        }
        double match_ratio =
            static_cast<double>(match_count) / this->_label.size();
        Logger::instance().debug("test acc: {}", match_ratio);
        for (auto [idx, d] : this->_label | std::views::enumerate) {
            auto [predict, confidence] = d;

            this->_data[idx * MaxPart].get()->Win.Header.Modulation = predict;
            this->_data[idx * MaxPart].get()->Win.Header.Confidence =
                std::uint8_t(confidence * 100);
        }
    }

    void SetLabel(batch_label_t input) {
        this->_label = input;
    }

    auto Data() {
        return this->_data;
    }
};
