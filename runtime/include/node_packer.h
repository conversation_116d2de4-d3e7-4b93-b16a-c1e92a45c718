#pragma once

#include <array>
#include <chrono>
#include <memory>
#include <queue>
#include <thread>

#include "logger.h"
#include "node_base.h"
#include "structs.h"

namespace DataFlow {
template <int IB, int OB, typename Queue>
class NodePacker : public ProcessNode<Queue> {
  private:
    using Base  = ProcessNode<Queue>;
    using OType = std::array<std::shared_ptr<ComputePackage>, OB>;
    using IType = std::array<std::shared_ptr<RawDatagram>, IB>;

    constexpr static int inputBatch  = IB;
    constexpr static int outputBatch = OB;
    constexpr static int inputSize =
        sizeof(std::remove_extent_t<RawDatagram>) * inputBatch;

  public:
    NodePacker() {
        Base::_inputQueues.resize(1);
        Base::_outputQueues.resize(1);
    }
    ~NodePacker() = default;

    int Start() {
        thread_ = std::thread(&NodePacker::loop_, this);
        SetThreadName(thread_, "NdPacker");
        return 0;
    }

    void Join() {
        thread_.join();
    }

    void loop_() {
        std::size_t id = 0;
        std::queue<std::shared_ptr<RawDatagram>> cache;
        std::shared_ptr<ComputePackage> package;

        auto newPackage = [&]() {
            std::size_t start = 0;
            if (package != nullptr) {
                start = package->Start() + MaxComputeParts;
            } else if (!cache.empty()) {
                auto buf = cache.front();
                start =
                    buf.get()->Win.index() / MaxComputeParts * MaxComputeParts;
            }
            package = std::make_shared<ComputePackage>(id++, start);

            while (!cache.empty()) {
                auto buf = cache.front();
                cache.pop();
                auto [success, needCache] = package->FillData(buf);
                if (!success) {
                    Logger::instance().warn(
                        "drop datagram when drop package index: {}",
                        buf->Win.index());
                    continue;
                }
            }
        };
        auto onDropPackage = [&]() {
            package.reset();
            id--;
            newPackage();
        };
        auto onCompleted = [&]() {
            Logger::instance().debug("push package check: (completed: {})",
                                 package->IsCompleted());

            auto array   = std::make_shared<OType>();
            array->at(0) = package;
            Base::_outputQueues[0]->push(array);
            newPackage();
        };

        auto lastPart{-1};
        while (true) {
            auto [data, exitFlag] =
                Base::_inputQueues[0]
                    ->template pop<
                        std::array<std::shared_ptr<RawDatagram>, IB>>();
            if (exitFlag) {
                break;
            }
            if (data == nullptr) {
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
                continue;
            }

            for (auto i = 0; i < inputBatch; i++) {
                auto buf = (data.get())->at(i);
                if (buf->Win.isValid()) {
                    std::size_t idx = buf->Win.index();
                    auto left       = idx % MaxComputeParts;
                    auto right      = (lastPart + 1) % MaxComputeParts;
                    if (lastPart != -1 && left != right) [[unlikely]] {
                        Logger::instance().info("prt data lost: jump {}->{}",
                                            lastPart, idx);
                    }
                    lastPart = idx;

                    // 起始包逻辑
                    if (package == nullptr) {
                        cache.push(buf);
                        newPackage();
                    }

                    auto [success, needCache] = package->FillData(buf);
                    if (!success) {
                        if (needCache) {
                            cache.push(buf);
                        } else {
                            Logger::instance().error(
                                "drop datagram when fill data index: {}", idx);
                        }
                    }
                    if (!package->IsCompleted()) {
                        if (cache.size() >= 20) {
                            Logger::instance().warn(
                                "drop package, because cache size: {}",
                                cache.size());
                            onDropPackage();
                        }
                        continue;
                    }
                    onCompleted();

                } else {
                    Logger::instance().error("invalid datagram header:{} {}",
                                         buf->RawData[0], buf->RawData[1]);
                    continue;
                }
            }
        }
    }

    std::thread thread_;
};
} // namespace DataFlow