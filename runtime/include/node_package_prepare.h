#pragma once

#include <array>
#include <chrono>
#include <memory>
#include <queue>
#include <thread>

#include "logger.h"
#include "node_base_batch.h"
#include "package.h"
#include "structs.h"

namespace DataFlow {
template <int B, typename Queue>
class NodePackagePrepare
    : public ProcessNodeBatch<ComputePackage, B, B, Queue> {
  private:
    using Base      = ProcessNode<Queue>;
    using BaseBatch = ProcessNodeBatch<ComputePackage, B, B, Queue>;

    constexpr static int batch = B;

  public:
    NodePackagePrepare() {
        Base::_inputQueues.resize(1);
        Base::_outputQueues.resize(1);
    }
    ~NodePackagePrepare() = default;

    int Start() {
        thread_ = std::thread(&NodePackagePrepare::loop_, this);
        SetThreadName(thread_, "NdPkgPrepare");
        return 0;
    }

    void Join() {
        thread_.join();
    }

    void loop_() {
        while (true) {
            auto exit = BaseBatch::popFromBatch(
                [this](std::shared_ptr<ComputePackage> ptr) {
                    ptr->Prepare();
                    if (BaseBatch::pushToBatch(ptr)) {
                        return true;
                    }
                    return false;
                });
            if (exit) {
                break;
            }
        }
    }

  private:
    std::thread thread_;
};
} // namespace DataFlow