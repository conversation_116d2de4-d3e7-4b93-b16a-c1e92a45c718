import os, sys
import pickle
import numpy as np
import pandas as pd
from scipy.signal import spectrogram
from scipy.signal import medfilt
import pywt
import argparse
import matplotlib.pyplot as plt


sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

output_dir = os.path.join(script_dir, 'output')
if not os.path.exists(output_dir):
    os.mkdir(output_dir)

# Loading the dataset
# RadioSamples2024.12.20.dat
# RML2016.10a_dict.pkl

parser = argparse.ArgumentParser(description='Modulation Analysis')
parser.add_argument('--len', type=int, default=128, help='Length of the input signal')
args = parser.parse_args()

sig_len = args.len
with open("../build-dataset/RadioSamples-rx{}.dat".format(sig_len),'rb') as file:
    Xd = pickle.load(file,encoding='bytes')

# Count the total number of training data samples
total_samples = sum(Xd[key].shape[0] for key in Xd.keys())
print("Total number of training data samples:", total_samples)

# Ploting for AM-SSB in I/Q plane
mods, snrs, rates = map(lambda j: sorted(list(set(map(lambda x: x[j], Xd.keys())))), [0,1,2])
print("MODs: ", mods)
print("SNRs: ", snrs)
print("RATs: ", rates)

for mod in mods:
    snr = 12
    rate1 = 4
    rate2 = 32
    show_mod = mod.decode(encoding='UTF-8')
    fig, axs = plt.subplots(4, 2, figsize=(20, 40))
    fig.suptitle("Data representation variance in {} SNR 12".format(show_mod))

    sample = np.random.randint(0, Xd[mod, snr, rate1].shape[0])
    sample1 = np.random.randint(0, Xd[mod, snr, rate2].shape[0])

    idata = Xd[mod, snr, rate1][sample, 0].astype(np.float32)
    qdata = Xd[mod, snr, rate1][sample, 1].astype(np.float32)
    
    # Assuming the IQ data is obtained after bandpass filtering around a 3GHz center frequency
    # with a sampling rate of 30MHz, calculate the frequency for each FFT point.
    center_frequency = 3e9  # 3 GHz
    sampling_rate = 30e6  # 30 MHz

    # Compute the FFT of the signal
    # Combine idata and qdata into a complex signal for joint frequency domain analysis
    signal = idata + 1j * qdata
    num = len(signal)

    # Apply Hamming window to the complex signal
    window = np.hanning(num)
    signal_windowed = signal * window
    fft_result = np.fft.fftshift(np.fft.fft(signal_windowed))

    # 寻找峰值
    freq_axis = np.fft.fftshift(np.fft.fftfreq(num, d=1 / sampling_rate))
    power = np.abs(fft_result) ** 2
    k_peak = np.argmax(power)
    f_fft = freq_axis[k_peak]


    # 三次样条插值修正
    if 0 < k_peak < num - 1:
        p1 = power[k_peak - 1]
        p2 = power[k_peak]
        p3 = power[k_peak + 1]
        delta_k = (p1 - p3) / (2 * p2 - p1 - p3)
        f_fft_interp = f_fft + delta_k * (sampling_rate / num)

    else:
        f_fft_interp = f_fft
    # Adjust the frequencies to account for the center frequency
    dominant_frequency = f_fft_interp + center_frequency
    print("Dominant Freq: {:.2f} Hz".format(dominant_frequency))

    # 计算占用带宽
    # 计算参考功率
    reference_power = np.sum(power)
    # 计算参考功率的0.5%
    threshold = 0.005 * reference_power

    # 从最低频率开始累加功率
    cumulative_power_low = 0
    for i in range(len(power)):
        cumulative_power_low += power[i]
        if cumulative_power_low >= threshold:
            first_frequency_index = i
            first_frequency = freq_axis[i]
            break

    # 从最高频率开始累加功率
    cumulative_power_high = 0
    for i in range(len(power) - 1, -1, -1):
        cumulative_power_high += power[i]
        if cumulative_power_high >= threshold:
            second_frequency_index = i
            second_frequency = freq_axis[i]
            break

    # 计算占用带宽
    occupied_bandwidth = np.abs(second_frequency - first_frequency)


    # 计算幅度
    magnitudes = np.sqrt(np.array(idata) ** 2 + np.array(qdata) ** 2)

    # 计算最大值、最小值和平均值
    max_level = np.max(magnitudes)
    min_level = np.min(magnitudes)
    average_level = np.mean(magnitudes)

    # 设置门限值，这里将平均值的1.5倍作为门限值，可根据实际情况调整
    threshold = 1.5 * average_level


    # 频偏：假设参考频率为fc，频偏就是测量频率与参考频率的差值
    frequency_offset = f_fft_interp

    phase_ref = np.pi  # 参考相位 180度
    # 相偏：计算信号的相位，这里简单假设参考相位为0
    phase = np.angle(signal)
    average_phase = np.mean(phase)
    phase_offset = average_phase - phase_ref

    ax = axs[3, 1]  # Reuse the second subplot in the first row for displaying dominant frequency
    ax.text(0.5, 0.5, 
            "Dominant Freq: {:.2f} Hz\nOccupied Bandwidth: {:.2f} Hz\nMax Power Level: {:.2f}\nMin Power Level: {:.2f}\nAverage Power Level: {:.2f}\nThreshold: {:.2f}\nFrequency Offset: {:.2f} Hz\nPhase Offset: {:.2f} rad".format(
                dominant_frequency, occupied_bandwidth, max_level, min_level, average_level, threshold, frequency_offset, phase_offset),
            fontsize=12, ha='center', va='center', transform=ax.transAxes,
            bbox=dict(facecolor='white', alpha=0.8, edgecolor='black'))
    ax.set_title("{} Median Filtered I Component with Dominant Frequency, Bandwidth, and Power Levels".format(show_mod))

    # Ploting mod in time domain
    ax = axs[0, 0]

    idata = Xd[mod, snr, rate1][sample, 0].astype(np.float32)
    qdata = Xd[mod, snr, rate1][sample, 1].astype(np.float32)
    # Calculating instantaneous amplitude and phase
    amp_sample_0 = np.sqrt(np.abs(idata**2 + qdata**2))

    idata = Xd[mod, snr, rate2][sample1, 0].astype(np.float32)
    qdata = Xd[mod, snr, rate2][sample1, 1].astype(np.float32)
    # Calculating instantaneous amplitude and phase
    amp_sample_1 = np.sqrt(np.abs(idata**2 + qdata**2))
    ax.plot(amp_sample_0, label=sample)
    ax.plot(amp_sample_1, label=sample1)
    ax.set_xlabel("Time")
    ax.set_ylabel("Amplitude")
    ax.set_title("{} Time Plot".format(show_mod))
    ax.grid(True, axis='both')
    ax.legend()


    # Applying median filter for denoising both I and Q components
    signal_i = Xd[mod, snr, rate1][sample, 0].astype(np.float32)
    signal_q = Xd[mod, snr, rate1][sample, 1].astype(np.float32)
    signal1_i = Xd[mod, snr, rate2][sample1, 0].astype(np.float32)
    signal1_q = Xd[mod, snr, rate2][sample1, 1].astype(np.float32)

    # Plotting the filtered I component
    ax = axs[0, 1]
    ax.plot(signal_i, label=f"{sample} I")
    ax.plot(signal1_i, label=f"{sample1} I")
    ax.set_xlabel("Time")
    ax.set_ylabel("Amplitude")
    ax.set_title("{} Median Filtered I Component".format(show_mod))
    ax.grid(True, axis='both')
    ax.legend()



    if mod == b'WBFM':
        size = Xd[mod, snr, rate1].shape[0]
        for idx in range(size):
            idata = medfilt(Xd[mod, snr, rate1][idx, 0].astype(np.float32), kernel_size=5)
            qdata = medfilt(Xd[mod, snr, rate1][idx, 1].astype(np.float32), kernel_size=5)
            # Calculating instantaneous amplitude and phase

            amp = np.sqrt(idata**2 + qdata**2)
            phs = np.arctan2(qdata, idata)
            sum_amp = np.sum(amp)

            # Zero-center normalization of instantaneous amplitude
            namp = (len(amp) * amp / sum_amp - 1)
            # Perform FFT and take the square
            fft_result = np.abs(np.fft.fft(namp)) ** 2

            # Find the maximum value as gamma_max
            gamma_max = np.max(fft_result)
            print("({}) Maximum value of zero-center normalized instantaneous amplitude spectral density (gamma):{}".format(idx, gamma_max))



    # Plotting for mod in I/Q plane
    ax = axs[1, 0]
    ax.scatter(signal_i, signal_q, c='blue', label=sample)
    ax.scatter(signal1_i, signal1_q, c='red', label=sample1)
    ax.set_xlabel("I")
    ax.set_ylabel("Q")
    ax.set_title("{} Constellation Diagram".format(show_mod))
    ax.legend()

    # Plotting power spectrum
    ax = axs[1, 1]
    X_f = np.fft.fftshift(np.fft.fft(signal_i))
    X_f1 = np.fft.fftshift(np.fft.fft(signal1_i))
    freqs = np.fft.fftshift(np.fft.fftfreq(len(signal_i)))
    power_spectrum = np.abs(X_f) ** 2
    power_spectrum1 = np.abs(X_f1) ** 2
    ax.plot(freqs, power_spectrum, label=sample)
    ax.plot(freqs, power_spectrum1, label=sample1)
    ax.set_xlabel("Frequency")
    ax.set_ylabel("Power")
    ax.set_title("{} Power Spectrum".format(show_mod))
    ax.grid(True, axis='both')
    ax.legend()

    # Plotting time-frequency analysis
    ax = axs[2, 0]
    f, t, Sxx = spectrogram(signal_i, fs=2.0, noverlap=16, nperseg=32)
    ax.pcolormesh(t, f, 10 * np.log10(Sxx), shading='gouraud')
    ax.set_ylabel('Frequency [Hz]')
    ax.set_xlabel('Time [sec]')
    ax.set_title("{} Time-Frequency Analysis({})".format(show_mod, sample))

    ax = axs[2, 1]
    f, t, Sxx = spectrogram(signal1_i, fs=2.0, noverlap=16, nperseg=32)
    ax.pcolormesh(t, f, 10 * np.log10(Sxx), shading='gouraud')
    ax.set_ylabel('Frequency [Hz]')
    ax.set_xlabel('Time [sec]')
    ax.set_title("{} Time-Frequency Analysis({})".format(show_mod, sample1))

    # Plotting time-frequency analysis using wavelet transform
    ax = axs[3, 0]
    scales = np.arange(1, 128)
    coef, freqs = pywt.cwt(signal_i, scales, 'cmor1.5-1.0')
    ax.imshow(np.abs(coef), extent=[0, len(signal_i), 1, len(signal_i)//2], cmap='jet', aspect='auto',
              vmax=abs(coef).max(), vmin=-abs(coef).max())
    ax.set_ylabel('Scale')
    ax.set_xlabel('Time')
    ax.set_title("{} Wavelet Transform({})".format(show_mod, sample))

    # ax = axs[3, 1]
    # scales = np.arange(1, 128)
    # coef, freqs = pywt.cwt(signal1_i, scales, 'cmor1.5-1.0')
    # ax.imshow(np.abs(coef), extent=[0, len(signal1_i), 1, len(signal1_i)//2], cmap='jet', aspect='auto',
    #           vmax=abs(coef).max(), vmin=-abs(coef).max())
    # ax.set_ylabel('Scale')
    # ax.set_xlabel('Time')
    # ax.set_title("{} Wavelet Transform({})".format(show_mod, sample1))

    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    plt.savefig("{}/{}_SNR{}_combined.png".format(output_dir, show_mod, snr, rates))
    plt.show()
    plt.close()

