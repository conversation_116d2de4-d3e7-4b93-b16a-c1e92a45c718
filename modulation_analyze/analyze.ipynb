{"cells": [{"cell_type": "code", "execution_count": 18, "metadata": {"_cell_guid": "b1076dfc-b9ad-4769-8c92-a6c4dae69d19", "_uuid": "8f2839f25d086af736a60e9eeb907d3b93b6e0e5"}, "outputs": [], "source": ["%matplotlib inline\n", "import os, random\n", "os.environ['KERAS_BACKEND'] = \"tensorflow\"\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import pickle, random, sys\n", "import pandas as pd\n", "\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["# Loading the dataset\n", "with open(\"../build-dataset/RML2016.10a_dict.pkl\",'rb') as file:\n", "    Xd = pickle.load(file,encoding='bytes')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ploting for AM-SSB in I/Q plane\n", "snrs,mods = map(lambda j: sorted(list(set(map(lambda x: x[j], Xd.keys())))), [1,0])\n", "X1 =[]  \n", "Y1=[]\n", "lbl = []\n", "str=b'AM-SSB'\n", "for mod in mods:\n", "    for snr in snrs:\n", "        if(mod==str and snr==16):\n", "            test =Xd[(mod,snr)]\n", "            X1.append(Xd[(mod,snr)])\n", "            for i in range(Xd[(mod,snr)].shape[0]):\n", "                lbl.append((mod,snr))\n", "                Y1.append([test[0]+1j*test[1]])\n", "X1 = np.vstack(X1)\n", "Y1 = np.vstack(Y1)\n", "\n", "\n", "df= pd.DataFrame(lbl,columns=[\"mod\",\"snr\"])\n", "df['snr'].value_counts()\n", "ind = []\n", "for i in range(0,df.shape[0]):\n", "  if(df['snr'][i]==16):\n", "    ind.append(i)\n", "    \n", "    \n", "for i in range(0,10,1):\n", "    x = X1[i][0]\n", "    y= X1[i][1]\n", "    fig = plt.figure()\n", "    plt.scatter(x,y,c='blue',label=i)\n", "    plt.xlabel(\"I\")\n", "    plt.ylabel(\"Q\")\n", "    plt.title(\"Data representation variance in AM-SSB SNR 16\")\n", "    plt.legend()\n", "    plt.show()\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["# Ploting AM-SSB in time domain\n", "plt.plot(Xd[b'AM-SSB',4][2,0])\n", "plt.plot(Xd[b'AM-SSB',8][2,0])\n", "plt.xlabel(\"Time\")\n", "plt.ylabel(\"Amplitude\")\n", "plt.title(\"AM-SSB Time Plot\")\n", "plt.grid(True, axis='both')"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["data = Xd[b'AM-SSB',4][2,0]\n", "power_sp = np.abs(np.fft.fft(data))**2\n", "time_step = 1\n", "fre = np.fft.fftfreq(len(power_sp),time_step)\n", "idx = np.argsort(fre)\n", "plt.plot(fre[idx],power_sp[idx])\n", "plt.xlabel(\"Frequency\")\n", "plt.ylabel(\"Power Spectrum\")\n", "plt.title(\"AM-SSB Power Spectrum\")\n", "plt.grid(True, axis='both')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ploting for 8PSK in I/Q plane\n", "snrs,mods = map(lambda j: sorted(list(set(map(lambda x: x[j], Xd.keys())))), [1,0])\n", "X1 =[]  \n", "Y1=[]\n", "lbl = []\n", "str=b'8PSK'\n", "for mod in mods:\n", "    for snr in snrs:\n", "        if(mod==str and snr==16):\n", "            test =Xd[(mod,snr)]\n", "            X1.append(Xd[(mod,snr)])\n", "            for i in range(Xd[(mod,snr)].shape[0]):\n", "                lbl.append((mod,snr))\n", "                Y1.append([test[0]+1j*test[1]])\n", "X1 = np.vstack(X1)\n", "Y1 = np.vstack(Y1)\n", "print(mods)\n", "print(snrs)\n", "\n", "\n", "\n", "df= pd.DataFrame(lbl,columns=[\"mod\",\"snr\"])\n", "df['snr'].value_counts()\n", "ind = []\n", "for i in range(0,df.shape[0]):\n", "  if(df['snr'][i]==16):\n", "    ind.append(i)\n", "    \n", "\n", "for i in range(0,10,1):\n", "    x = X1[i][0]\n", "    y= X1[i][1]\n", "    fig = plt.figure()\n", "    plt.scatter(x,y,c='blue',label=i)\n", "    plt.xlabel(\"I\")\n", "    plt.ylabel(\"Q\")\n", "    plt.title(\"Data representation variance in 8PSK SNR 16\")\n", "    plt.legend()\n", "    plt.show()\n", "\n"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["# Ploting 8PSK in time domain\n", "plt.plot(Xd[b'8PSK',4][2,0])\n", "plt.plot(Xd[b'8PSK',8][2,0])\n", "plt.xlabel(\"Time\")\n", "plt.ylabel(\"Amplitude\")\n", "plt.title(\"8PSK Time Plot\")\n", "plt.grid(True, axis='both')"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["data = Xd[b'8PSK',4][2,0]\n", "power_sp = np.abs(np.fft.fft(data))**2\n", "time_step = 1\n", "fre = np.fft.fftfreq(len(power_sp),time_step)\n", "idx = np.argsort(fre)\n", "plt.plot(fre[idx],power_sp[idx])\n", "plt.xlabel(\"Frequency\")\n", "plt.ylabel(\"Power Spectrum\")\n", "plt.title(\"8PSK Power Spectrum\")\n", "plt.grid(True, axis='both')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ploting for AM-DSB in I/Q plane\n", "snrs,mods = map(lambda j: sorted(list(set(map(lambda x: x[j], Xd.keys())))), [1,0])\n", "X1 =[]  \n", "Y1=[]\n", "lbl = []\n", "str=b'AM-DSB'\n", "for mod in mods:\n", "    for snr in snrs:\n", "        if(mod==str and snr==16):\n", "            test =Xd[(mod,snr)]\n", "            X1.append(Xd[(mod,snr)])\n", "            for i in range(Xd[(mod,snr)].shape[0]):\n", "                lbl.append((mod,snr))\n", "                Y1.append([test[0]+1j*test[1]])\n", "X1 = np.vstack(X1)\n", "Y1 = np.vstack(Y1)\n", "print(mods)\n", "print(snrs)\n", "\n", "\n", "\n", "df= pd.DataFrame(lbl,columns=[\"mod\",\"snr\"])\n", "df['snr'].value_counts()\n", "ind = []\n", "for i in range(0,df.shape[0]):\n", "  if(df['snr'][i]==16):\n", "    ind.append(i)\n", "    \n", "\n", "for i in range(0,10,1):\n", "    x = X1[i][0]\n", "    y= X1[i][1]\n", "    fig = plt.figure()\n", "    plt.scatter(x,y,c='blue',label=i)\n", "    plt.xlabel(\"I\")\n", "    plt.ylabel(\"Q\")\n", "    plt.title(\"Data representation variance in AM-DSB SNR 16\")\n", "    plt.legend()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["# Ploting AM-DSB in time domain\n", "plt.plot(Xd[b'AM-DSB',4][2,0])\n", "plt.plot(Xd[b'AM-DSB',8][2,0])\n", "plt.xlabel(\"Time\")\n", "plt.ylabel(\"Amplitude\")\n", "plt.title(\"AM-DSB Time Plot\")\n", "plt.grid(True, axis='both')"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["data = Xd[b'AM-DSB',4][2,0]\n", "power_sp = np.abs(np.fft.fft(data))**2\n", "time_step = 1\n", "fre = np.fft.fftfreq(len(power_sp),time_step)\n", "idx = np.argsort(fre)\n", "plt.plot(fre[idx],power_sp[idx])\n", "plt.xlabel(\"Frequency\")\n", "plt.ylabel(\"Power Spectrum\")\n", "plt.title(\"AM-DSB Power Spectrum\")\n", "plt.grid(True, axis='both')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ploting for CPFSK in I/Q plane\n", "snrs,mods = map(lambda j: sorted(list(set(map(lambda x: x[j], Xd.keys())))), [1,0])\n", "X1 =[]  \n", "Y1=[]\n", "lbl = []\n", "str=b'CPFSK'\n", "for mod in mods:\n", "    for snr in snrs:\n", "        if(mod==str and snr==16):\n", "            test =Xd[(mod,snr)]\n", "            X1.append(Xd[(mod,snr)])\n", "            for i in range(Xd[(mod,snr)].shape[0]):\n", "                lbl.append((mod,snr))\n", "                Y1.append([test[0]+1j*test[1]])\n", "X1 = np.vstack(X1)\n", "Y1 = np.vstack(Y1)\n", "print(mods)\n", "print(snrs)\n", "\n", "\n", "\n", "df= pd.DataFrame(lbl,columns=[\"mod\",\"snr\"])\n", "df['snr'].value_counts()\n", "ind = []\n", "for i in range(0,df.shape[0]):\n", "  if(df['snr'][i]==16):\n", "    ind.append(i)\n", "    \n", "\n", "for i in range(0,10,1):\n", "    x = X1[i][0]\n", "    y= X1[i][1]\n", "    fig = plt.figure()\n", "    plt.scatter(x,y,c='blue',label=i)\n", "    plt.xlabel(\"I\")\n", "    plt.ylabel(\"Q\")\n", "    plt.title(\"Data representation variance in CPFSK SNR 16\")\n", "    plt.legend()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["# Ploting CPFSK in time domain\n", "plt.plot(Xd[b'CPFSK',4][2,0])\n", "plt.plot(Xd[b'CPFSK',8][2,0])\n", "plt.xlabel(\"Time\")\n", "plt.ylabel(\"Amplitude\")\n", "plt.title(\"CPFSK Time Plot\")\n", "plt.grid(True, axis='both')"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [], "source": ["data = Xd[b'CPFSK',4][2,0]\n", "power_sp = np.abs(np.fft.fft(data))**2\n", "time_step = 1\n", "fre = np.fft.fftfreq(len(power_sp),time_step)\n", "idx = np.argsort(fre)\n", "plt.plot(fre[idx],power_sp[idx])\n", "plt.xlabel(\"Frequency\")\n", "plt.ylabel(\"Power Spectrum\")\n", "plt.title(\"CPFSK Power Spectrum\")\n", "plt.grid(True, axis='both')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ploting for BPSK in I/Q plane\n", "snrs,mods = map(lambda j: sorted(list(set(map(lambda x: x[j], Xd.keys())))), [1,0])\n", "X1 =[]  \n", "Y1=[]\n", "lbl = []\n", "str=b'BPSK'\n", "for mod in mods:\n", "    for snr in snrs:\n", "        if(mod==str and snr==16):\n", "            test =Xd[(mod,snr)]\n", "            X1.append(Xd[(mod,snr)])\n", "            for i in range(Xd[(mod,snr)].shape[0]):\n", "                lbl.append((mod,snr))\n", "                Y1.append([test[0]+1j*test[1]])\n", "X1 = np.vstack(X1)\n", "Y1 = np.vstack(Y1)\n", "print(mods)\n", "print(snrs)\n", "\n", "\n", "\n", "df= pd.DataFrame(lbl,columns=[\"mod\",\"snr\"])\n", "df['snr'].value_counts()\n", "ind = []\n", "for i in range(0,df.shape[0]):\n", "  if(df['snr'][i]==16):\n", "    ind.append(i)\n", "    \n", "\n", "for i in range(0,10,1):\n", "    x = X1[i][0]\n", "    y= X1[i][1]\n", "    fig = plt.figure()\n", "    plt.scatter(x,y,c='blue',label=i)\n", "    plt.xlabel(\"I\")\n", "    plt.ylabel(\"Q\")\n", "    plt.title(\"Data representation variance in BPSK SNR 16\")\n", "    plt.legend()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["# Ploting BPSK in time domain\n", "plt.plot(Xd[b'BPSK',4][2,0])\n", "plt.plot(Xd[b'BPSK',8][2,0])\n", "plt.xlabel(\"Time\")\n", "plt.ylabel(\"Amplitude\")\n", "plt.title(\"BPSK Time Plot\")\n", "plt.grid(True, axis='both')"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["data = Xd[b'BPSK',4][2,0]\n", "power_sp = np.abs(np.fft.fft(data))**2\n", "time_step = 1\n", "fre = np.fft.fftfreq(len(power_sp),time_step)\n", "idx = np.argsort(fre)\n", "plt.plot(fre[idx],power_sp[idx])\n", "plt.xlabel(\"Frequency\")\n", "plt.ylabel(\"Power Spectrum\")\n", "plt.title(\"BPSK Power Spectrum\")\n", "plt.grid(True, axis='both')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ploting for PAM4 in I/Q plane\n", "snrs,mods = map(lambda j: sorted(list(set(map(lambda x: x[j], Xd.keys())))), [1,0])\n", "X1 =[]  \n", "Y1=[]\n", "lbl = []\n", "str=b'PAM4'\n", "for mod in mods:\n", "    for snr in snrs:\n", "        if(mod==str and snr==16):\n", "            test =Xd[(mod,snr)]\n", "            X1.append(Xd[(mod,snr)])\n", "            for i in range(Xd[(mod,snr)].shape[0]):\n", "                lbl.append((mod,snr))\n", "                Y1.append([test[0]+1j*test[1]])\n", "X1 = np.vstack(X1)\n", "Y1 = np.vstack(Y1)\n", "print(mods)\n", "print(snrs)\n", "\n", "\n", "\n", "df= pd.DataFrame(lbl,columns=[\"mod\",\"snr\"])\n", "df['snr'].value_counts()\n", "ind = []\n", "for i in range(0,df.shape[0]):\n", "  if(df['snr'][i]==16):\n", "    ind.append(i)\n", "    \n", "\n", "for i in range(0,10,1):\n", "    x = X1[i][0]\n", "    y= X1[i][1]\n", "    fig = plt.figure()\n", "    plt.scatter(x,y,c='blue',label=i)\n", "    plt.xlabel(\"I\")\n", "    plt.ylabel(\"Q\")\n", "    plt.title(\"Data representation variance in PAM4 SNR 16\")\n", "    plt.legend()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [], "source": ["# Ploting PAM4 in time domain\n", "plt.plot(Xd[b'PAM4',4][2,0])\n", "plt.plot(Xd[b'PAM4',8][2,0])\n", "plt.xlabel(\"Time\")\n", "plt.ylabel(\"Amplitude\")\n", "plt.title(\"PAM4 Time Plot\")\n", "plt.grid(True, axis='both')"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [], "source": ["data = Xd[b'PAM4',4][2,0]\n", "power_sp = np.abs(np.fft.fft(data))**2\n", "time_step = 1\n", "fre = np.fft.fftfreq(len(power_sp),time_step)\n", "idx = np.argsort(fre)\n", "plt.plot(fre[idx],power_sp[idx])\n", "plt.xlabel(\"Frequency\")\n", "plt.ylabel(\"Power Spectrum\")\n", "plt.title(\"PAM4 Power Spectrum\")\n", "plt.grid(True, axis='both')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ploting for QAM64 in I/Q plane\n", "snrs,mods = map(lambda j: sorted(list(set(map(lambda x: x[j], Xd.keys())))), [1,0])\n", "X1 =[]  \n", "Y1=[]\n", "lbl = []\n", "str=b'QAM64'\n", "for mod in mods:\n", "    for snr in snrs:\n", "        if(mod==str and snr==16):\n", "            test =Xd[(mod,snr)]\n", "            X1.append(Xd[(mod,snr)])\n", "            for i in range(Xd[(mod,snr)].shape[0]):\n", "                lbl.append((mod,snr))\n", "                Y1.append([test[0]+1j*test[1]])\n", "X1 = np.vstack(X1)\n", "Y1 = np.vstack(Y1)\n", "print(mods)\n", "print(snrs)\n", "\n", "\n", "\n", "df= pd.DataFrame(lbl,columns=[\"mod\",\"snr\"])\n", "df['snr'].value_counts()\n", "ind = []\n", "for i in range(0,df.shape[0]):\n", "  if(df['snr'][i]==16):\n", "    ind.append(i)\n", "    \n", "\n", "for i in range(0,10,1):\n", "    x = X1[i][0]\n", "    y= X1[i][1]\n", "    fig = plt.figure()\n", "    plt.scatter(x,y,c='blue',label=i)\n", "    plt.xlabel(\"I\")\n", "    plt.ylabel(\"Q\")\n", "    plt.title(\"Data representation variance in QAM64 SNR 16\")\n", "    plt.legend()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ploting for QAM64 in time domain\n", "plt.plot(Xd[b'QAM64',4][6,0])\n", "plt.plot(Xd[b'QAM64',4][7,0])\n", "plt.xlabel(\"Time\")\n", "plt.ylabel(\"Amplitude\")\n", "plt.title(\"QAM64 Time Plot\")\n", "plt.grid(b=True, axis='both')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = Xd[b'QAM64',4][6,0]\n", "power_sp = np.abs(np.fft.fft(data))**2\n", "time_step = 1\n", "fre = np.fft.fftfreq(len(power_sp),time_step)\n", "idx = np.argsort(fre)\n", "plt.plot(fre[idx],power_sp[idx])\n", "plt.xlabel(\"Frequency\")\n", "plt.ylabel(\"Power Spectrum\")\n", "plt.title(\"QAM64 Power Spectrum\")\n", "plt.grid(b=True, axis='both')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ploting for GFSK in I/Q plane\n", "snrs,mods = map(lambda j: sorted(list(set(map(lambda x: x[j], Xd.keys())))), [1,0])\n", "X1 =[]  \n", "Y1=[]\n", "lbl = []\n", "str=b'GFSK'\n", "for mod in mods:\n", "    for snr in snrs:\n", "        if(mod==str and snr==16):\n", "            test =Xd[(mod,snr)]\n", "            X1.append(Xd[(mod,snr)])\n", "            for i in range(Xd[(mod,snr)].shape[0]):\n", "                lbl.append((mod,snr))\n", "                Y1.append([test[0]+1j*test[1]])\n", "X1 = np.vstack(X1)\n", "Y1 = np.vstack(Y1)\n", "print(mods)\n", "print(snrs)\n", "\n", "\n", "\n", "df= pd.DataFrame(lbl,columns=[\"mod\",\"snr\"])\n", "df['snr'].value_counts()\n", "ind = []\n", "for i in range(0,df.shape[0]):\n", "  if(df['snr'][i]==16):\n", "    ind.append(i)\n", "    \n", "\n", "for i in range(0,10,1):\n", "    x = X1[i][0]\n", "    y= X1[i][1]\n", "    fig = plt.figure()\n", "    plt.scatter(x,y,c='blue',label=i)\n", "    plt.xlabel(\"I\")\n", "    plt.ylabel(\"Q\")\n", "    plt.title(\"Data representation variance in GFSK SNR 16\")\n", "    plt.legend()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ploting GFSK in time domain\n", "plt.plot(Xd[b'GFSK',4][2,0])\n", "plt.plot(Xd[b'GFSK',8][2,0])\n", "plt.xlabel(\"Time\")\n", "plt.ylabel(\"Amplitude\")\n", "plt.title(\"GFSK Time Plot\")\n", "plt.grid(b=True, axis='both')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = Xd[b'GFSK',4][2,0]\n", "power_sp = np.abs(np.fft.fft(data))**2\n", "time_step = 1\n", "fre = np.fft.fftfreq(len(power_sp),time_step)\n", "idx = np.argsort(fre)\n", "plt.plot(fre[idx],power_sp[idx])\n", "plt.xlabel(\"Frequency\")\n", "plt.ylabel(\"Power Spectrum\")\n", "plt.title(\"GFSK Power Spectrum\")\n", "plt.grid(b=True, axis='both')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ploting for QAM16 in I/Q plane\n", "snrs,mods = map(lambda j: sorted(list(set(map(lambda x: x[j], Xd.keys())))), [1,0])\n", "X1 =[]  \n", "Y1=[]\n", "lbl = []\n", "str=b'QAM16'\n", "for mod in mods:\n", "    for snr in snrs:\n", "        if(mod==str and snr==16):\n", "            test =Xd[(mod,snr)]\n", "            X1.append(Xd[(mod,snr)])\n", "            for i in range(Xd[(mod,snr)].shape[0]):\n", "                lbl.append((mod,snr))\n", "                Y1.append([test[0]+1j*test[1]])\n", "X1 = np.vstack(X1)\n", "Y1 = np.vstack(Y1)\n", "print(mods)\n", "print(snrs)\n", "\n", "\n", "\n", "df= pd.DataFrame(lbl,columns=[\"mod\",\"snr\"])\n", "df['snr'].value_counts()\n", "ind = []\n", "for i in range(0,df.shape[0]):\n", "  if(df['snr'][i]==16):\n", "    ind.append(i)\n", "    \n", "\n", "for i in range(0,10,1):\n", "    x = X1[i][0]\n", "    y= X1[i][1]\n", "    fig = plt.figure()\n", "    plt.scatter(x,y,c='blue',label=i)\n", "    plt.xlabel(\"I\")\n", "    plt.ylabel(\"Q\")\n", "    plt.title(\"Data representation variance in QAM16 SNR 16\")\n", "    plt.legend()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ploting for QAM16 in time domain\n", "plt.plot(Xd[b'QAM16',4][2,0])\n", "plt.plot(Xd[b'QAM16',8][2,0])\n", "plt.xlabel(\"Time\")\n", "plt.ylabel(\"Amplitude\")\n", "plt.title(\"QAM16 Time Plot\")\n", "plt.grid(b=True, axis='both')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = Xd[b'QAM16',4][2,0]\n", "power_sp = np.abs(np.fft.fft(data))**2\n", "time_step = 1\n", "fre = np.fft.fftfreq(len(power_sp),time_step)\n", "idx = np.argsort(fre)\n", "plt.plot(fre[idx],power_sp[idx])\n", "plt.xlabel(\"Frequency\")\n", "plt.ylabel(\"Power Spectrum\")\n", "plt.title(\"QAM16 Power Spectrum\")\n", "plt.grid(b=True, axis='both')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ploting for QPSK in I/Q plane\n", "snrs,mods = map(lambda j: sorted(list(set(map(lambda x: x[j], Xd.keys())))), [1,0])\n", "X1 =[]  \n", "Y1=[]\n", "lbl = []\n", "str=b'QPSK'\n", "for mod in mods:\n", "    for snr in snrs:\n", "        if(mod==str and snr==16):\n", "            test =Xd[(mod,snr)]\n", "            X1.append(Xd[(mod,snr)])\n", "            for i in range(Xd[(mod,snr)].shape[0]):\n", "                lbl.append((mod,snr))\n", "                Y1.append([test[0]+1j*test[1]])\n", "X1 = np.vstack(X1)\n", "Y1 = np.vstack(Y1)\n", "print(mods)\n", "print(snrs)\n", "\n", "\n", "\n", "df= pd.DataFrame(lbl,columns=[\"mod\",\"snr\"])\n", "df['snr'].value_counts()\n", "ind = []\n", "for i in range(0,df.shape[0]):\n", "  if(df['snr'][i]==16):\n", "    ind.append(i)\n", "    \n", "\n", "for i in range(0,10,1):\n", "    x = X1[i][0]\n", "    y= X1[i][1]\n", "    fig = plt.figure()\n", "    plt.scatter(x,y,c='blue',label=i)\n", "    plt.xlabel(\"I\")\n", "    plt.ylabel(\"Q\")\n", "    plt.title(\"Data representation variance in QPSK SNR 16\")\n", "    plt.legend()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ploting for QPSK in time domain\n", "plt.plot(Xd[b'QPSK',4][2,0])\n", "plt.plot(Xd[b'QPSK',8][2,0])\n", "plt.xlabel(\"Time\")\n", "plt.ylabel(\"Amplitude\")\n", "plt.title(\"QPSK Time Plot\")\n", "plt.grid(b=True, axis='both')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = Xd[b'QPSK',4][2,0]\n", "power_sp = np.abs(np.fft.fft(data))**2\n", "time_step = 1\n", "fre = np.fft.fftfreq(len(power_sp),time_step)\n", "idx = np.argsort(fre)\n", "plt.plot(fre[idx],power_sp[idx])\n", "plt.xlabel(\"Frequency\")\n", "plt.ylabel(\"Power Spectrum\")\n", "plt.title(\"QPSK Power Spectrum\")\n", "plt.grid(b=True, axis='both')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ploting for WBFM in I/Q plane\n", "snrs,mods = map(lambda j: sorted(list(set(map(lambda x: x[j], Xd.keys())))), [1,0])\n", "X1 =[]  \n", "Y1=[]\n", "lbl = []\n", "str=b'WBFM'\n", "for mod in mods:\n", "    for snr in snrs:\n", "        if(mod==str and snr==16):\n", "            test =Xd[(mod,snr)]\n", "            X1.append(Xd[(mod,snr)])\n", "            for i in range(Xd[(mod,snr)].shape[0]):\n", "                lbl.append((mod,snr))\n", "                Y1.append([test[0]+1j*test[1]])\n", "X1 = np.vstack(X1)\n", "Y1 = np.vstack(Y1)\n", "print(mods)\n", "print(snrs)\n", "\n", "\n", "\n", "df= pd.DataFrame(lbl,columns=[\"mod\",\"snr\"])\n", "df['snr'].value_counts()\n", "ind = []\n", "for i in range(0,df.shape[0]):\n", "  if(df['snr'][i]==16):\n", "    ind.append(i)\n", "    \n", "\n", "for i in range(0,10,1):\n", "    x = X1[i][0]\n", "    y= X1[i][1]\n", "    fig = plt.figure()\n", "    plt.scatter(x,y,c='blue',label=i)\n", "    plt.xlabel(\"I\")\n", "    plt.ylabel(\"Q\")\n", "    plt.title(\"Data representation variance in WBFM SNR 16\")\n", "    plt.legend()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ploting for WBFM in time domain\n", "plt.plot(Xd[b'WBFM',4][2,0])\n", "plt.plot(Xd[b'WBFM',8][2,0])\n", "plt.xlabel(\"Time\")\n", "plt.ylabel(\"Amplitude\")\n", "plt.title(\"WBFM Time Plot\")\n", "plt.grid(b=True, axis='both')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = Xd[b'WBFM',4][2,0]\n", "power_sp = np.abs(np.fft.fft(data))**2\n", "time_step = 1\n", "fre = np.fft.fftfreq(len(power_sp),time_step)\n", "idx = np.argsort(fre)\n", "plt.plot(fre[idx],power_sp[idx])\n", "plt.xlabel(\"Frequency\")\n", "plt.ylabel(\"Power Spectrum\")\n", "plt.title(\"WBFM Power Spectrum\")\n", "plt.grid(b=True, axis='both')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["snrs,mods = map(lambda j: sorted(list(set(map(lambda x: x[j], Xd.keys())))), [1,0])\n", "X = []  \n", "lbl = []\n", "for mod in mods:\n", "    for snr in snrs:\n", "        X.append(Xd[(mod,snr)])\n", "        for i in range(Xd[(mod,snr)].shape[0]):  lbl.append((mod,snr))\n", "X = np.vstack(X)\n", "print(mods)\n", "print(snrs)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["#  into training and test sets of the form we can train/test on \n", "#  while keeping SNR and Mod labels handy for each\n", "np.random.seed(2016)\n", "n_examples = X.shape[0]\n", "n_train = n_examples // 2\n", "train_idx = np.random.choice(range(0,n_examples), size=n_train, replace=False)\n", "test_idx = list(set(range(0,n_examples))-set(train_idx))\n", "X_train = X[train_idx]\n", "X_test =  X[test_idx]\n", "def to_onehot(yy):\n", "    yy1 = np.zeros([len(yy), max(yy)+1])\n", "    yy1[np.arange(len(yy)),yy] = 1\n", "    return yy1\n", "Y_train = to_onehot(list(map(lambda x: mods.index(lbl[x][0]), train_idx)))\n", "Y_test = to_onehot(list(map(lambda x: mods.index(lbl[x][0]), test_idx)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["in_shp = list(X_train.shape[1:])\n", "print(X_train.shape, in_shp)\n", "classes = mods"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tensorflow.keras.models import Sequential\n", "from tensorflow.keras.callbacks import EarlyStopping\n", "from tensorflow.keras.layers import Dense, Dropout, Activation, Flatten, Conv2D, MaxPooling2D , Reshape , ZeroPadding2D,BatchNormalization,LSTM\n", "\n", "\n", "dr = 0.4 # dropout rate (%)\n", "model = Sequential()\n", "model.add(Reshape([1]+in_shp, input_shape=in_shp))\n", "model.add(ZeroPadding2D((0, 2)))\n", "model.add(Conv2D(256, 1, 3, padding='valid', activation=\"relu\", name=\"conv1\", kernel_initializer='glorot_uniform',data_format='channels_first'))\n", "model.add(Dropout(dr))\n", "model.add(ZeroPadding2D((0, 2)))\n", "model.add(Conv2D(80, 2, 3, padding='valid', activation=\"relu\", name=\"conv2\", kernel_initializer='glorot_uniform',data_format='channels_first'))\n", "model.add(Dropout(dr))\n", "model.add(<PERSON><PERSON>())\n", "model.add(Den<PERSON>(256, activation='relu', kernel_initializer='he_normal', name=\"dense1\"))\n", "model.add(Dropout(dr))\n", "model.add(Den<PERSON>( len(classes), kernel_initializer='he_normal', name=\"dense2\" ))\n", "model.add(Activation('softmax'))\n", "model.add(Reshape([len(classes)]))\n", "model.compile(loss='categorical_crossentropy', optimizer='adam')\n", "model.summary()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tensorflow.keras.models import Sequential\n", "from tensorflow.keras.layers import Dense, Dropout, Activation, Flatten, Conv2D, MaxPooling2D , Reshape , ZeroPadding2D,BatchNormalization\n", "from tensorflow.keras.callbacks import EarlyStopping\n", "# Set up some params \n", "nb_epoch = 200    # number of epochs to train on\n", "batch_size = 1024  # training batch size\n", "\n", "# perform training ...\n", "#   - call the main training loop in keras for our network+dataset\n", "filepath = 'convmodrecnets_CNN2_0.5.wts.h5'\n", "history = model.fit(X_train,\n", "    Y_train,\n", "    batch_size=batch_size,\n", "    epochs=nb_epoch,\n", "   # show_accuracy=False,\n", "    verbose=2,\n", "    validation_data=(X_test, Y_test),\n", "    callbacks = [\n", "        keras.callbacks.ModelCheckpoint(filepath, monitor='val_loss', verbose=0, save_best_only=True, mode='auto'),\n", "        keras.callbacks.EarlyStopping(monitor='val_loss', patience=5, verbose=0, mode='auto')\n", "    ])\n", "# we re-load the best weights once training is finished\n", "model.load_weights(filepath)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Show simple version of performance\n", "score = model.evaluate(X_test, Y_test,  verbose=0, batch_size=batch_size)\n", "print (score)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Show loss curves \n", "plt.figure()\n", "plt.title('Training performance')\n", "plt.plot(history.epoch, history.history['loss'], label='train loss+error')\n", "plt.plot(history.epoch, history.history['val_loss'], label='val_error')\n", "plt.legend()"]}, {"cell_type": "code", "execution_count": 179, "metadata": {}, "outputs": [], "source": ["def plot_confusion_matrix(cm, title='Confusion matrix', cmap=plt.cm.Blues, labels=[]):\n", "    plt.imshow(cm, interpolation='nearest', cmap=cmap)\n", "    plt.title(title)\n", "    plt.colorbar()\n", "    tick_marks = np.arange(len(labels))\n", "    plt.xticks(tick_marks, labels, rotation=45)\n", "    plt.yticks(tick_marks, labels)\n", "    plt.tight_layout()\n", "    plt.ylabel('True label')\n", "    plt.xlabel('Predicted label')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot confusion matrix\n", "test_Y_hat = model.predict(X_test, batch_size=batch_size)\n", "conf = np.zeros([len(classes),len(classes)])\n", "confnorm = np.zeros([len(classes),len(classes)])\n", "for i in range(0,X_test.shape[0]):\n", "    j = list(Y_test[i,:]).index(1)\n", "    k = int(np.argmax(test_Y_hat[i,:]))\n", "    conf[j,k] = conf[j,k] + 1\n", "for i in range(0,len(classes)):\n", "    confnorm[i,:] = conf[i,:] / np.sum(conf[i,:])\n", "plot_confusion_matrix(confnorm, labels=classes)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# Plot confusion matrix\n", "acc = {}\n", "for snr in snrs:\n", "\n", "    # extract classes @ SNR\n", "    test_SNRs = list(map(lambda x: lbl[x][1], test_idx))\n", "    test_X_i = X_test[np.where(np.array(test_SNRs)==snr)]\n", "    test_Y_i = Y_test[np.where(np.array(test_SNRs)==snr)]    \n", "\n", "    # estimate classes\n", "    test_Y_i_hat = model.predict(test_X_i)\n", "    conf = np.zeros([len(classes),len(classes)])\n", "    confnorm = np.zeros([len(classes),len(classes)])\n", "    for i in range(0,test_X_i.shape[0]):\n", "        j = list(test_Y_i[i,:]).index(1)\n", "        k = int(np.argmax(test_Y_i_hat[i,:]))\n", "        conf[j,k] = conf[j,k] + 1\n", "    for i in range(0,len(classes)):\n", "        confnorm[i,:] = conf[i,:] / np.sum(conf[i,:])\n", "    \n", "    \n", "    plt.figure()\n", "    plot_confusion_matrix(confnorm, labels=classes, title=\"ConvNet Confusion Matrix (SNR=%d)\"%(snr))\n", "       \n", "        \n", "    cor = np.sum(np.diag(conf))\n", "    ncor = np.sum(conf) - cor\n", "    print(\"SNR: \", snr)\n", "    print(\"Overall Accuracy: \", cor / (cor+ncor))\n", "    \n", "    acc[snr] = 1.0*cor/(cor+ncor)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.plot(snrs, list(map(lambda x: acc[x], snrs)))\n", "plt.xlabel(\"Signal to Noise Ratio\")\n", "plt.ylabel(\"Classification Accuracy\")\n", "plt.title(\"CNN Classification Accuracy on dataset RadioML 2016.10 Alpha\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}