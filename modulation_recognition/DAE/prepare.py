import numpy as np
from scipy.signal import medfilt

def normalize_amp(x, axis=-1):
    y = np.sum(x ** 2, axis, keepdims=True)
    return x / np.sqrt(y)

def normalize_phs(X):
    # normalize the phase to [-1,1]
    for i in range(X.shape[0]):
        k = 2 / (X[i, :, 1].max() - X[i, :, 1].min())
        X[i, :, 1] = -1 + k * (X[i, :, 1] - X[i, :, 1].min())
    return X

def to_amp_phase(X):
    X_cmplx = X[:,:,0] + 1j* X[:,:,1]
    X_amp = np.abs(X_cmplx)
    X_phs = np.arctan2(X[:,:,1],X[:,:,0])/np.pi
    X_amp = np.reshape(X_amp,(X_amp.shape[0], X_amp.shape[1], 1))
    X_phs = np.reshape(X_phs,(X_phs.shape[0], X_phs.shape[1], 1))
    out = np.concatenate((X_amp, X_phs), axis=2) 
    return out

def prepare(X_train, X_val, X_test):
    print("DAE prepare")

    # Apply median filter to smooth the data
    X_train = prepare_single(X_train)
    X_val = prepare_single(X_val)
    X_test = prepare_single(X_test)

    return X_train, X_val, X_test

def prepare_single(X_data):
    X_data = X_data.astype(np.float32)
    # Apply median filter to smooth the data
    X_data = medfilt(X_data, kernel_size=(1, 5, 1))
    X_data = to_amp_phase(X_data)

    X_data[:, :, 0] = normalize_amp(X_data[:, :, 0])
    X_data = normalize_phs(X_data)

    return X_data
