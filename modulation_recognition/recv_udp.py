import socket
import struct
import numpy as np
import time

TELEGRAM_DATA_LEN = 1024  # TelegramDataLen
TELEGRAM_HEADER_LEN = 8  # sizeof(WinHeader)
TELEGRAM_LEN = TELEGRAM_DATA_LEN + TELEGRAM_HEADER_LEN  # TelegramDataLen + sizeof(WinHeader)
TELEGRAM_IQ_LEN = TELEGRAM_DATA_LEN // 2  # TelegramDataLen / sizeof(int16_t)
WINDOW_IQ_LEN = 1024  # WindowDataLen

class WinHeader:
    def __init__(self, type, win_num, part_num, mod):
        self.magic = 0x5b
        self.type = type
        self.win_num = win_num
        self.part_num = part_num
        self.modulation = mod
        self.reserved1 = 0
        self.reserved2 = 0

    def pack(self):
        return struct.pack('BBHBBBB', self.magic, self.type, self.win_num, self.part_num, self.modulation, self.reserved1, self.reserved2)

    @classmethod
    def unpack(cls, data):
        magic, type, win_num, part_num, modulation, reserved1, reserved2 = struct.unpack('BBHBBBB', data)
        if magic != 0x5b:
            raise ValueError("Invalid magic number")
        return cls(type, win_num, part_num, modulation)

class WinData:
    def __init__(self, header, iq_data):
        self.header = header
        self.iq_data = iq_data

    def pack(self):
        return self.header.pack() + struct.pack(f'{TELEGRAM_DATA_LEN}B', *self.iq_data)

    @classmethod
    def unpack(cls, data):
        header_data = data[:TELEGRAM_HEADER_LEN]
        iq_data = data[TELEGRAM_HEADER_LEN:]
        header = WinHeader.unpack(header_data)
        iq_data = np.frombuffer(iq_data, dtype=np.uint8)
        return cls(header, iq_data)

# 发送 UDP 包
udp_ip = "0.0.0.0"
udp_port = 2552
sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

def recv_udp():
    sock.bind((udp_ip, udp_port))
    packet_count = 0
    total_bytes = 0
    start_time = time.time()

    while True:
        data, _ = sock.recvfrom(TELEGRAM_LEN)
        packet_count += 1
        total_bytes += len(data)
        
        try:
            win_data = WinData.unpack(data)
            # print(f"Received packet from {addr}: {win_data}")
        except ValueError as e:
            print(e)
        
        current_time = time.time()
        elapsed_time = current_time - start_time
        if elapsed_time >= 1.0:
            rate = packet_count / elapsed_time
            bandwidth = (total_bytes * 8) / elapsed_time  # in bits per second
            print(f"Rate: {rate:.2f} packets/sec, Bandwidth: {bandwidth/1024/1024:.2f} Mbps")
            packet_count = 0
            total_bytes = 0
            start_time = current_time

recv_udp()
