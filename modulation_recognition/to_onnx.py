import tensorflow as tf
from keras.api.models import load_model
import tf2onnx
import argparse
import os

import DAE as mdlDAE
import PET_CGDNN as mdlPET
from keras.utils import plot_model


# Get the directory of the current script
current_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(current_dir)

# Parse command line arguments
parser = argparse.ArgumentParser(description='Convert Keras model to ONNX format.')
parser.add_argument('--len', type=int, required=True, help='Length of the model')
parser.add_argument('--model', type=str, required=True, help='Name of the model')
args = parser.parse_args()

sig_len = args.len
model_name = args.model

args = parser.parse_args()
if args.model == 'DAE':
    print("use DAE")
    mdl = mdlDAE
elif args.model == 'PET_CGDNN':
    print("use PET_CGDNN")
    mdl = mdlPET
else:
    print("Invalid model choice. Use 'DAE' or 'PET_CGDNN'.")
    os.exit(1)

model_test = load_model('weights/{}_{}.keras'.format(model_name, sig_len))
# Get the number of output classes from model_test
output_layer = model_test.layers[-2]
print(output_layer)
# Get the output shape of the output layer
output_shape = output_layer.output.shape
num_classes = output_shape[-1]  # The last dimension of the output shape is the number of classes
print(f"Number of output classes: {num_classes}")



model = mdl.get_model(
    weights=None, input_shape=[sig_len, 2], 
    input_shape2=[sig_len], 
    classes=num_classes,
    use_cudnn=False,
)
model.compile(
    loss='categorical_crossentropy',
    metrics=['accuracy'], 
    optimizer='adam'
)

# Load the Keras model
model.load_weights('weights/{}_{}.keras'.format(model_name, sig_len))
model.summary()
# Visualize the model's architecture and save it as a flowchart

flowchart_path = 'weights/{}_{}_flowchart.png'.format(model_name, sig_len)
plot_model(model, to_file=flowchart_path, show_shapes=True, show_layer_names=True)
print(f"Model flowchart has been saved to {flowchart_path}")

input_signature = [tf.TensorSpec([None, sig_len, 2], tf.float32, name='input')]

# Convert the model to ONNX format with opset 13
onnx_model, _ = tf2onnx.convert.from_keras(model, input_signature, opset=15)

# Save the ONNX model
onnx_model_path = 'weights/{}_{}.onnx'.format(model_name, sig_len)
with open(onnx_model_path, "wb") as f:
    f.write(onnx_model.SerializeToString())

print(f"Model has been converted to ONNX format and saved to {onnx_model_path}")