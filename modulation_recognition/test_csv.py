import os, sys
import pickle
import numpy as np
import pandas as pd
import argparse
import random
import matplotlib.pyplot as plt
import onnxruntime as ort

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
import common
import DAE as mdlDAE
import PET_CGDNN as mdlPET

script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

output_dir = os.path.join(script_dir, 'output')
if not os.path.exists(output_dir):
    os.mkdir(output_dir)


parser = argparse.ArgumentParser()
parser.add_argument('--mode', choices=['train', 'predict', 'both'], required=False, default='both',
                    help="Mode to run the script in: 'train', 'predict', or 'both', default is 'both'")
parser.add_argument('--model', choices=['DAE', 'PET_CGDNN'], required=False, default='DAE',
                    help="Model to use: 'DAE' or 'PET_CGDNN'")
parser.add_argument('--len', type=int, default=128, help='Length of the input signal')
parser.add_argument('--prepare', action='store_true', help='Whether to execute the prepare operation')

args = parser.parse_args()
if args.model == 'DAE':
    print("use DAE")
    mdl = mdlDAE
elif args.model == 'PET_CGDNN':
    print("use PET_CGDNN")
    mdl = mdlPET
else:
    print("Invalid model choice. Use 'DAE' or 'PET_CGDNN'.")
    os.exit(1)

sig_len = args.len
model_name = args.model

available_providers = ort.get_available_providers()
print(f"Available Providers: {available_providers}")
device = ort.get_device()
print(f"Current Device: {device}")

# Load the ONNX model
model_path = 'weights/{}_{}.onnx'.format(model_name, sig_len)
session = ort.InferenceSession(model_path, providers=['CUDAExecutionProvider'])




# Get model input and output names
input_name = session.get_inputs()[0].name
output_name = session.get_outputs()[0].name

# Read signal data from ./output/test_1024.csv
prefix = '' if args.prepare else 'prepared_'
data_path = os.path.join(output_dir, 'test_{}{}.csv'.format(prefix, 1024))
signal_data = pd.read_csv(data_path)
# Initialize counters for success and failure
success_count = 0
failure_count = 0

# Iterate over each row in signal_data and print the Modulation
for index, row in signal_data.iterrows():
    signal_data_i_str = row['I_Samples']
    signal_data_q_str = row['Q_Samples']
    signal_data_i = list(map(float, signal_data_i_str.split('|')))
    signal_data_q = list(map(float, signal_data_q_str.split('|')))
    signal_data_combined = np.stack((signal_data_i, signal_data_q), axis=-1)
    signal_data_combined = np.expand_dims(signal_data_combined, axis=0)

    X_data = signal_data_combined.astype(np.float32)
    if args.prepare:
        X_data = mdl.prepare_single(X_data)

    Y = session.run([output_name], {input_name: X_data})[0]
    Y_test = int(np.argmax(Y[0,:]))
    
    if Y_test == row['label']:
        success_count += 1
    else:
        failure_count += 1

    # print(f"index: {index}, predict:{Y_test}, label: {row['label']}, equal: {Y_test == row['label']}")

# Print the success and failure counts
print(f"Success count: {success_count}")
print(f"Failure count: {failure_count}")
# Calculate and print the success rate
total_count = success_count + failure_count
success_rate = (success_count / total_count) * 100 if total_count > 0 else 0
print(f"Success rate: {success_rate:.2f}%")