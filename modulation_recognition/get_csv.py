import os, sys
import pickle
import numpy as np
import pandas as pd
import argparse
import random
import matplotlib.pyplot as plt

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
import common
import DAE as mdlDAE
import PET_CGDNN as mdlPET

script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

output_dir = os.path.join(script_dir, 'output')
if not os.path.exists(output_dir):
    os.mkdir(output_dir)


parser = argparse.ArgumentParser()
parser.add_argument('--mode', choices=['train', 'predict', 'both'], required=False, default='both',
                    help="Mode to run the script in: 'train', 'predict', or 'both', default is 'both'")
parser.add_argument('--model', choices=['DAE', 'PET_CGDNN'], required=False, default='DAE',
                    help="Model to use: 'DAE' or 'PET_CGDNN'")
parser.add_argument('--len', type=int, default=128, help='Length of the input signal')

args = parser.parse_args()
if args.model == 'DAE':
    print("use DAE")
    mdl = mdlDAE
elif args.model == 'PET_CGDNN':
    print("use PET_CGDNN")
    mdl = mdlPET
else:
    print("Invalid model choice. Use 'DAE' or 'PET_CGDNN'.")
    os.exit(1)



sig_len = args.len
# 加载模型
(mods, snrs, rates, lbl), (X_train, Y_train), (X_val, Y_val), (X_test, Y_test), (train_idx, val_idx, test_idx) = \
    common.load_data("../build-dataset/RadioSamples-rx{}.dat".format(sig_len)) # RML2016.10a_dict.pkl

X_train_prepared, X_val_prepared, X_test_prepared = mdl.prepare(X_train, X_val, X_test)


snr = 12
count = 0


test_SNRs = [lbl[x][1] for x in test_idx]
test_X_i = X_test[np.where(np.array(test_SNRs) == snr)]
test_Y_i = Y_test[np.where(np.array(test_SNRs) == snr)]
testp_X_i = X_test_prepared[np.where(np.array(test_SNRs) == snr)]
testp_Y_i = Y_test[np.where(np.array(test_SNRs) == snr)]
test_idx = np.array(test_idx)
test_idx_i = test_idx[np.where(np.array(test_SNRs) == snr)]

data = []
prepared_data = []
print(len(test_X_i))
print(len(test_Y_i))
print(len(test_idx_i))

indexs = list(range(len(test_X_i)))
random.shuffle(indexs)

for count, idx in enumerate(indexs):
    if count >= 1024:
        break

    pi_samples = ""
    pq_samples = ""

    for sample in testp_X_i[idx]:
        pi_samples += str(sample[0]) + "|"
        pq_samples += str(sample[1]) + "|"
    pi_samples = pi_samples.rstrip("|")
    pq_samples = pq_samples.rstrip("|")



    i_samples = ""
    q_samples = ""

    for sample in test_X_i[idx]:
        i_samples += str(sample[0]) + "|"
        q_samples += str(sample[1]) + "|"
    i_samples = i_samples.rstrip("|")
    q_samples = q_samples.rstrip("|")

    lbl_idx = test_idx_i[idx]

    Y_test_value = int(np.argmax(test_Y_i[idx,:]))
    print(f"Iteration {count}: Modulation: {lbl[lbl_idx][0].decode('utf-8')}, label: {Y_test_value}, SNR: {lbl[lbl_idx][1]}, Rate: {lbl[lbl_idx][2]}")

    data.append([lbl[lbl_idx][0].decode('utf-8'), Y_test_value,
                 lbl[lbl_idx][1], lbl[lbl_idx][2], i_samples, q_samples])
    
    prepared_data.append([lbl[lbl_idx][0].decode('utf-8'), Y_test_value,
                 lbl[lbl_idx][1], lbl[lbl_idx][2], pi_samples, pq_samples])


csv_path = os.path.join(output_dir, 'test_{}.csv'.format(sig_len))

# Create DataFrame and write to CSV
df_split = pd.DataFrame(data, columns=['Modulation', 'label', 'SNR', 'Rate', 'I_Samples', 'Q_Samples'])
df_split.to_csv(csv_path, index=False)
print(f"Random samples written to {csv_path}")


csv_path = os.path.join(output_dir, 'test_prepared_{}.csv'.format(sig_len))
# Create DataFrame and write to CSV
df_split = pd.DataFrame(prepared_data, columns=['Modulation', 'label', 'SNR', 'Rate', 'I_Samples', 'Q_Samples'])
df_split.to_csv(csv_path, index=False)
print(f"Random samples written to {csv_path}")



