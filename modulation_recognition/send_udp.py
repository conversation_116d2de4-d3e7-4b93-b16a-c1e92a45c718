import socket
import struct
import time
import os
import numpy as np
import pandas as pd

# 定义 RawDatagram 结构的格式

TELEGRAM_DATA_LEN = 1024  # TelegramDataLen
TELEGRAM_HEADER_LEN = 16  # sizeof(WinHeader)
TELEGRAM_LEN = TELEGRAM_DATA_LEN + TELEGRAM_HEADER_LEN  # TelegramDataLen + sizeof(WinHeader)
TELEGRAM_IQ_LEN = TELEGRAM_DATA_LEN // 2  # TelegramDataLen / sizeof(int16_t)
WINDOW_IQ_LEN = 1024  # WindowDataLen

class WinHeader:
    def __init__(self, type, win_num, part_num, mod):
        self.magic = 0x5b
        self.type = type
        self.win_num = win_num
        self.part_num = part_num
        self.modulation = mod
        self.confidence = 0
        self.level = 20
        self.carrier_freq = 1.0e9
        self.sample_rate = 50.0e6


    def pack(self):
        # 这里一共12个字节:
        # 'B' (uint8) * 2 = 2 bytes
        # 'H' (uint16) * 1 = 2 bytes
        # 'B' (uint8) * 4 = 4 bytes
        # 'f' (float32) * 2 = 8 bytes
        # 总计: 2 + 2 + 4 + 8 = 16 bytes
        return struct.pack('BBHBBBBff', self.magic, self.type, self.win_num, self.part_num, self.modulation, self.confidence, self.level, self.carrier_freq, self.sample_rate)

class WinData:
    def __init__(self, header, iq_data):
        self.header = header
        self.iq_data = iq_data

    def pack(self):
        return self.header.pack() + struct.pack(f'{TELEGRAM_DATA_LEN}B', *self.iq_data)

def calc(data):
    data = data.astype(np.float32)
    idata = data[0, :, 0]
    qdata = data[0, :, 1]

    center_frequency = 1.0e9 
    sampling_rate = 50.0e6 

    # Compute the FFT of the signal
    # Combine idata and qdata into a complex signal for joint frequency domain analysis
    signal = idata + 1j * qdata
    num = len(signal)

    # Apply Hamming window to the complex signal
    window = np.hanning(num)
    signal_windowed = signal * window
    fft_result = np.fft.fftshift(np.fft.fft(signal_windowed))

    # 寻找峰值
    freq_axis = np.fft.fftshift(np.fft.fftfreq(num, d=1 / sampling_rate))
    power = np.abs(fft_result) ** 2
    k_peak = np.argmax(power)
    f_fft = freq_axis[k_peak]


    # 三次样条插值修正
    if 0 < k_peak < num - 1:
        p1 = power[k_peak - 1]
        p2 = power[k_peak]
        p3 = power[k_peak + 1]
        delta_k = (p1 - p3) / (2 * p2 - p1 - p3)
        f_fft_interp = f_fft + delta_k * (sampling_rate / num)

    else:
        f_fft_interp = f_fft
    # Adjust the frequencies to account for the center frequency
    dominant_frequency = f_fft_interp + center_frequency

    # 计算占用带宽
    # 计算参考功率
    reference_power = np.sum(power)
    # 计算参考功率的0.5%
    threshold = 0.005 * reference_power

    # 从最低频率开始累加功率
    cumulative_power_low = 0
    for i in range(len(power)):
        cumulative_power_low += power[i]
        if cumulative_power_low >= threshold:
            first_frequency = freq_axis[i]
            break

    # 从最高频率开始累加功率
    cumulative_power_high = 0
    for i in range(len(power) - 1, -1, -1):
        cumulative_power_high += power[i]
        if cumulative_power_high >= threshold:
            second_frequency = freq_axis[i]
            break

    # 计算占用带宽
    occupied_bandwidth = np.abs(second_frequency - first_frequency)


    # 计算幅度
    magnitudes = np.sqrt(np.array(idata) ** 2 + np.array(qdata) ** 2)

    # 计算最大值、最小值和平均值
    max_level = np.max(magnitudes)
    min_level = np.min(magnitudes)
    average_level = np.mean(magnitudes)

    # 频偏：假设参考频率为fc，频偏就是测量频率与参考频率的差值
    frequency_offset = f_fft_interp

    phase_ref = np.pi  # 参考相位 180度
    # 相偏：计算信号的相位，这里简单假设参考相位为0
    phase = np.angle(signal)
    average_phase = np.mean(phase)
    phase_offset = average_phase - phase_ref

    print(f"Max Power Level: {max_level}")
    print(f"Min Power Level: {min_level}")
    print(f"Average Power Level: {average_level}")
    print(f"Dominant Freq: {dominant_frequency} Hz")
    print(f"Occupied Bandwidth: {occupied_bandwidth} Hz")
    print(f"Frequency Offset: {frequency_offset} Hz")
    print(f"Phase Offset: {phase_offset} rad")



# 发送 UDP 包
udp_ip = "127.0.0.1"
udp_port = 2551
sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

part = 0
win = 0

script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

output_dir = os.path.join(script_dir, 'output')
if not os.path.exists(output_dir):
    os.mkdir(output_dir)


# Read signal data from ./output/test_1024.csv
data_path = os.path.join(output_dir, 'test_1024.csv')
signal_data = pd.read_csv(data_path)

X_data = []
Y_data = []
# Iterate over each row in signal_data and print the Modulation
for index, row in signal_data.iterrows():
    signal_modulation = row['label']
    signal_data_i_str = row['I_Samples']
    signal_data_q_str = row['Q_Samples']
    signal_data_i = list(map(float, signal_data_i_str.split('|')))
    signal_data_q = list(map(float, signal_data_q_str.split('|')))
    signal_data_combined = np.stack((signal_data_i, signal_data_q), axis=-1)
    signal_data_combined = np.expand_dims(signal_data_combined, axis=0)

    X_data.append(signal_data_combined.astype(np.float32))
    Y_data.append(signal_modulation)


X_data = np.array(X_data)
X_data = X_data.astype(np.int16)

win_idx = -1
packet_count = 0
start_time = time.time()

test_idx = 0
can_test = True

calc(X_data[test_idx])



while True:
    for idx, iq_data in enumerate(X_data):
        win_idx += 1
        if win_idx > 65535:
            win_idx = 0
        iq_data_bytes = iq_data.tobytes()
        iq_data_segments = [iq_data_bytes[i:i + TELEGRAM_DATA_LEN] for i in range(0, len(iq_data_bytes), TELEGRAM_DATA_LEN)]
        mod = Y_data[idx]

        typ = 1
        if idx == test_idx and can_test:
            print(f"Sending packet {idx} with modulation {mod}")
            typ = 2
            can_test = False

        for part_num, segment in enumerate(iq_data_segments):
            header = WinHeader(type=typ, win_num=win_idx, part_num=part_num, mod=mod)
            win_data = WinData(header, segment)
            data = win_data.pack()
            sock.sendto(data, (udp_ip, udp_port))
            packet_count += 1
            # time.sleep(0.001)

    # Calculate and print the sending speed every second
    elapsed_time = time.time() - start_time
    if elapsed_time >= 1.0:
        print(f"Sending speed: {packet_count / elapsed_time:.2f} packets per second")
        packet_count = 0
        can_test = True
        start_time = time.time()
