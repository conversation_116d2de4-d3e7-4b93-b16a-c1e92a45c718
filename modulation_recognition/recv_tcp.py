import socket
import struct
import numpy as np
import time

TELEGRAM_DATA_LEN = 1024  # TelegramDataLen
TELEGRAM_HEADER_LEN = 40  # sizeof(WinHeader)
TELEGRAM_LEN = TELEGRAM_DATA_LEN + TELEGRAM_HEADER_LEN  # TelegramDataLen + sizeof(WinHeader)
TELEGRAM_IQ_LEN = TELEGRAM_DATA_LEN // 2  # TelegramDataLen / sizeof(int16_t)
WINDOW_IQ_LEN = 1024  # WindowDataLen

class WinHeader:
    def __init__(self, type, win_num, modulation, confidence, level, body_size, level_max, level_min, level_avg, dominant_freq, band_width, freq_offset, phase_offset):
        self.magic = 0x5c
        self.type = type
        self.win_num = win_num
        self.modulation = modulation
        self.confidence = confidence
        self.level = level
        self.reserved = 0
        self.body_size = body_size
        self.level_max = level_max
        self.level_min = level_min
        self.level_avg = level_avg
        self.dominant_freq = dominant_freq
        self.band_width = band_width
        self.freq_offset = freq_offset
        self.phase_offset = phase_offset

    @classmethod
    def unpack(cls, data):
        # 解释长度：
        # 'B' (uint8) * 6 = 6 bytes
        # 'H' (uint16) * 1 = 2 bytes
        # 'I' (uint32) * 1 = 4 bytes
        # 'f' (float32) * 7 = 28 bytes
        # 总计: 7 + 2 + 28 = 37 bytes
        magic, type, win_num, modulation, confidence, level, reserved, body_size, level_max, level_min, level_avg, dominant_freq, band_width, freq_offset, phase_offset = struct.unpack('BBHBBBBIfffffff', data)
        if magic != 0x5c:
            raise ValueError("Invalid magic number")
        return cls(type, win_num, modulation, confidence, level, body_size, level_max, level_min, level_avg, dominant_freq, band_width, freq_offset, phase_offset)

class WinData:
    def __init__(self, header, iq_data):
        self.header = header
        self.iq_data = iq_data

    @classmethod
    def unpack(cls, header, data):
        len = header.body_size
        iq_data = struct.unpack(f'{len}B', data[:len])
        return cls(header, iq_data)

# 发送 TCP 包
tcp_ip = "0.0.0.0"
tcp_port = 2552
sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

def recv_tcp():
    sock.bind((tcp_ip, tcp_port))
    sock.listen(1)
    conn, addr = sock.accept()
    print(f"Connection from {addr}")
    packet_count = 0
    total_bytes = 0
    start_time = time.time()

    while True:
        header_data = conn.recv(TELEGRAM_HEADER_LEN, socket.MSG_WAITALL)
        if not header_data:
            continue
        header = WinHeader.unpack(header_data)
        body_data = conn.recv(header.body_size, socket.MSG_WAITALL)
        if not body_data:
            continue
        data = header_data + body_data
        packet_count += 1
        total_bytes += len(data)

        if header.type == 2:
            print(f"Modulation: {header.modulation}")
            print(f"Confidence: {header.confidence}")
            print(f"Level Max: {header.level_max}")
            print(f"Level Min: {header.level_min}")
            print(f"Level Avg: {header.level_avg}")
            print(f"Dominant Frequency: {header.dominant_freq}")
            print(f"Bandwidth: {header.band_width}")
            print(f"Frequency Offset: {header.freq_offset}")
            print(f"Phase Offset: {header.phase_offset}")
        
        # try:
        #     win_data = WinData.unpack(header, body_data)
        #     # print(f"Received packet from {addr}: {win_data}")
        # except ValueError as e:
        #     print(e)
        
        current_time = time.time()
        elapsed_time = current_time - start_time
        if elapsed_time >= 1.0:
            rate = packet_count / elapsed_time
            bandwidth = (total_bytes * 8) / elapsed_time  # in bits per second
            print(f"Rate: {rate:.2f} packets/sec, Bandwidth: {bandwidth/1024/1024:.2f} Mbps")
            packet_count = 0
            total_bytes = 0
            start_time = current_time

    conn.close()

while True:
    recv_tcp()