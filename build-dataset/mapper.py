import numpy as np
from gnuradio import gr
# Define the constellation symbols
from enum import Enum

class ModTypes(Enum):
    BPSK = ('BPSK', 1)
    P2BPSK = ('P2BPSK', 1)
    QPSK = ('QPSK', 2)
    PSK8 = ('PSK8', 3)
    PI4DQPSK = ('PI4DQPSK', 2)
    PAM4 = ('PAM4', 2)
    QAM16 = ('QAM16', 4)
    QAM64 = ('QAM64', 6)

    def __str__(self):
        return self.value[0]

    @property
    def bits(self):
        return self.value[1]

sym_bpsk = np.array([1 + 0j, -1 + 0j])
sym_bpsk_off = np.array([0 + 1j, 0 - 1j])

sym_qpsk = np.array([1 + 0j, 0 + 1j, -1 + 0j, 0 - 1j])

sym_8psk = np.array([
    1 + 0j, 
    np.sqrt(1/2) + 1j*np.sqrt(1/2), 
    0 + 1j, 
    -np.sqrt(1/2) + 1j*np.sqrt(1/2), 
    -1 + 0j, 
    -np.sqrt(1/2) - 1j*np.sqrt(1/2), 
    0 - 1j, 
    np.sqrt(1/2) - 1j*np.sqrt(1/2)
])

sym_pam4 = np.array([-3 + 0j, -1 + 0j, 1 + 0j, 3 + 0j])
sym_qam16 = np.array([
    -3 + 3j, -1 + 3j, 1 + 3j, 3 + 3j,
    -3 + 1j, -1 + 1j, 1 + 1j, 3 + 1j,
    -3 - 1j, -1 - 1j, 1 - 1j, 3 - 1j,
    -3 - 3j, -1 - 3j, 1 - 3j, 3 - 3j
])
sym_qam64 = np.array([
    -7 + 7j, -5 + 7j, -3 + 7j, -1 + 7j, 1 + 7j, 3 + 7j, 5 + 7j, 7 + 7j,
    -7 + 5j, -5 + 5j, -3 + 5j, -1 + 5j, 1 + 5j, 3 + 5j, 5 + 5j, 7 + 5j,
    -7 + 3j, -5 + 3j, -3 + 3j, -1 + 3j, 1 + 3j, 3 + 3j, 5 + 3j, 7 + 3j,
    -7 + 1j, -5 + 1j, -3 + 1j, -1 + 1j, 1 + 1j, 3 + 1j, 5 + 1j, 7 + 1j,
    -7 - 1j, -5 - 1j, -3 - 1j, -1 - 1j, 1 - 1j, 3 - 1j, 5 - 1j, 7 - 1j,
    -7 - 3j, -5 - 3j, -3 - 3j, -1 - 3j, 1 - 3j, 3 - 3j, 5 - 3j, 7 - 3j,
    -7 - 5j, -5 - 5j, -3 - 5j, -1 - 5j, 1 - 5j, 3 - 5j, 5 - 5j, 7 - 5j,
    -7 - 7j, -5 - 7j, -3 - 7j, -1 - 7j, 1 - 7j, 3 - 7j, 5 - 7j, 7 - 7j
])

sym_dict = {
    ModTypes.BPSK: sym_bpsk,
    ModTypes.P2BPSK: sym_bpsk_off,
    ModTypes.QPSK: sym_8psk,
    ModTypes.PSK8: sym_8psk,
    ModTypes.PAM4: sym_pam4,
    ModTypes.QAM16: sym_qam16,
    ModTypes.QAM64: sym_qam64
}


class SymbolMapper(gr.sync_block):
    def __init__(self, modtype, symbols=[]):
        gr.sync_block.__init__(self,
            name="symbol_mapper_{0}".format(modtype),
            in_sig=[np.uint8],
            out_sig=[np.complex64])
        if modtype not in sym_dict:
            raise ValueError("Unsupported modulation type: {}".format(modtype))
        self.modtype = modtype
        self.symvals = sym_dict[modtype]
        if len(symbols) > 0:
            self.symvals = self.symvals[symbols]

    def work(self, input_items, output_items):
        output_items[0][:] = self.symvals[input_items[0]]
        return len(input_items[0])
