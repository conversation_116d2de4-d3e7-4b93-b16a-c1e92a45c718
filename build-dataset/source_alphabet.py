#!/usr/bin/env python
from gnuradio import gr, blocks
import numpy as np
from pydub import AudioSegment

sound_array_cache = None  # Global cache for sound array

class source_alphabet_discrete(gr.hier_block2):
    def __init__(self, limit=10000, randomize=False):
        gr.hier_block2.__init__(self, "source_alphabet_discrete",
            gr.io_signature(0,0,0),
            gr.io_signature(1,1,gr.sizeof_char))

        # 读取文件
        self.src = blocks.file_source(gr.sizeof_char, "source_material/gutenberg_shakespeare.txt")

        # 解包成一个 bit 的数据流
        self.convert = blocks.packed_to_unpacked_bb(1, gr.GR_MSB_FIRST)
        self.connect(self.src, self.convert)
        last = self.convert

        # 使用随机块乱序器（可选）使我们的序列白化
        if(randomize):
            rand_len = 256
            rand_bits = np.random.randint(2, size=rand_len)

            # 可重放的 随机数据 以保持流的长度
            self.randsrc = blocks.vector_source_b(rand_bits, True)

            # 对随机数据进行 xor 以创建白噪声流
            self.xor = blocks.xor_bb()
            self.connect(self.randsrc,(self.xor,1))
            self.connect(last, self.xor)
            last = self.xor

        # 8个数位压缩保存到一个字节
        self.pack = blocks.unpacked_to_packed_bb(1, gr.GR_MSB_FIRST)

        # blocks.head 用于限制流经流的项目数量。
        # 它保留 'limit' 个数位，实际上截断流以防止处理不必要的项目。
        self.limit = blocks.head(gr.sizeof_char, limit//8) # 8 bits per byte
        self.connect(last, self.pack, self.limit, self)

class source_alphabet_continuous(gr.hier_block2):
    def __init__(self, limit=10000):
        global sound_array_cache  # Use global cache
        gr.hier_block2.__init__(self, "source_alphabet_continuous",
            gr.io_signature(0,0,0),
            gr.io_signature(1,1,gr.sizeof_float))

        if sound_array_cache is None:
            sound = AudioSegment.from_mp3("source_material/serial-s01-e01.mp3")
            read = np.array(sound.get_array_of_samples(), dtype=np.int16)
            max_storage = limit * 100
            sound_array_cache = read[:max_storage]

        # 不重放的数据流
        self.src = blocks.vector_source_s(sound_array_cache, False)
        # 将短整数转换为复数
        self.to_complex = blocks.interleaved_short_to_complex()
        # 乘以 1/65535
        self.multiply = blocks.multiply_const_cc(1.0/65535)

        # 将复数转换为浮点
        self.to_float = blocks.complex_to_float()

        # 限制 limit 数量，从头开始读取
        self.limit = blocks.head(gr.sizeof_float, limit)
        self.connect(self.src, self.to_complex, self.multiply, self.to_float)
        last = self.to_float

        # connect head or not, and connect to output
        self.connect(last, self.limit, self)

if __name__ == "__main__":
    print("QA...")

