#!/usr/bin/env python
# -*- coding: utf-8 -*-

import time
import math
import numpy as np
from scipy.signal import get_window
from gnuradio import gr, blocks, digital, analog, filter
from mapper import SymbolMapper, ModTypes
from grextend import DebugBlockBB, gfsk4_mod, gfsk8_mod, cw_morse_mod

SAMPLING_RATE = 8
EXCESS_BW = 0.35
BT_MAX = 100
BT_NORMAL = 0.4



class TransmitterMapper(gr.hier_block2):
    def __init__(self, modtype: ModTypes, symvals, txname, samples_per_symbol=SAMPLING_RATE, excess_bw=EXCESS_BW):
        gr.hier_block2.__init__(self, txname,
            gr.io_signature(1, 1, gr.sizeof_char),
            gr.io_signature(1, 1, gr.sizeof_gr_complex))

        # unpack 输出指定位数的数据
        self.unpack = blocks.packed_to_unpacked_bb(modtype.bits, gr.GR_MSB_FIRST)
        self.mod = SymbolMapper(modtype, symvals)
        # pulse shaping filter
        nfilts = 32
        ntaps = nfilts * 11 * int(samples_per_symbol)    # make nfilts filters of ntaps each
        rrc_taps = filter.firdes.root_raised_cosine(
            nfilts,          # gain
            nfilts,          # sampling rate based on 32 filters in resampler
            1.0,             # symbol rate
            excess_bw, # excess bandwidth (roll-off factor)
            ntaps)
        self.rrc_filter = filter.pfb_arb_resampler_ccf(samples_per_symbol, rrc_taps)
        self.connect(self, self.unpack, self.mod, self.rrc_filter, self)


class TransmitterBPSKMapper(TransmitterMapper):
    modname = "{0}_mapper".format(str(ModTypes.BPSK))
    bits = ModTypes.BPSK.bits
    def __init__(self):
        TransmitterMapper.__init__(self, ModTypes.BPSK,
            [0,1], "transmitter_{0}".format(self.modname), SAMPLING_RATE, EXCESS_BW)

class TransmitterQPSKMapper(TransmitterMapper):
    modname = "{0}_mapper".format(str(ModTypes.QPSK))
    bits = ModTypes.QPSK.bits
    def __init__(self):
        TransmitterMapper.__init__(self, ModTypes.QPSK,
            [0,1,3,2], "transmitter_{0}".format(self.modname), SAMPLING_RATE, EXCESS_BW)

class Transmitter8PSKMapper(TransmitterMapper):
    modname = "{0}_mapper".format(str(ModTypes.PSK8))
    bits = ModTypes.PSK8.bits

    def __init__(self):
        TransmitterMapper.__init__(self, ModTypes.PSK8,
            [0,1,3,2,7,6,4,5], "transmitter_{0}".format(self.modname), SAMPLING_RATE, EXCESS_BW)

class TransmitterPAM4Mapper(TransmitterMapper):
    modname = "{0}_mapper".format(str(ModTypes.PAM4))
    bits = ModTypes.PAM4.bits

    def __init__(self):
        TransmitterMapper.__init__(self, ModTypes.PAM4,
            [0,1,3,2], "transmitter_{0}".format(self.modname), SAMPLING_RATE, EXCESS_BW)

class TransmitterQAM16Mapper(TransmitterMapper):
    modname = "{0}_mapper".format(str(ModTypes.QAM16))
    bits = ModTypes.QAM16.bits

    def __init__(self):
        TransmitterMapper.__init__(self, ModTypes.QAM16,
            [2,6,14,10,3,7,15,11,1,5,13,9,0,4,12,8], 
            "transmitter_{0}".format(self.modname), SAMPLING_RATE, EXCESS_BW)

class TransmitterQAM64Mapper(TransmitterMapper):
    modname = "{0}_mapper".format(str(ModTypes.QAM64))
    bits = ModTypes.QAM64.bits
    def __init__(self):
        TransmitterMapper.__init__(self, ModTypes.QAM64,
            [0,32,8,40,3,35,11,43,
             48,16,56,24,51,19,59,27,
            12,44,4,36,15,47,7,39,
            60,28,52,20,63,31,55,23,
            2,34,10,42,1,33,9,41,
            50,18,58,26,49,17,57,25,
            14,46,6,38,13,45,5,37,
            62,30,54,22,61,29,53,21], "transmitter_{0}".format(self.modname), SAMPLING_RATE, EXCESS_BW)


class TransmitterBPSK(gr.hier_block2):
    modname = str(ModTypes.BPSK)
    bits = ModTypes.BPSK.bits
    def __init__(self, samples_per_symbol=SAMPLING_RATE):
        gr.hier_block2.__init__(self, "transmitter_{0}".format(self.modname),
            gr.io_signature(1, 1, gr.sizeof_char),
            gr.io_signature(1, 1, gr.sizeof_gr_complex))
        self.mod = digital.psk.psk_mod(
            constellation_points=2,
            mod_code="gray",
            differential=False,
            samples_per_symbol=samples_per_symbol,
            excess_bw=EXCESS_BW,
            verbose=False,
            log=False
        )
        self.connect( self, self.mod, self )



class TransmitterQPSK(gr.hier_block2):
    modname = str(ModTypes.QPSK)
    bits = ModTypes.QPSK.bits

    def __init__(self, samples_per_symbol=SAMPLING_RATE):
        gr.hier_block2.__init__(self, "transmitter_{0}".format(self.modname),
            gr.io_signature(1, 1, gr.sizeof_char),
            gr.io_signature(1, 1, gr.sizeof_gr_complex))
        self.mod = digital.psk.psk_mod(
            constellation_points=4,
            mod_code="gray",
            differential=False,
            samples_per_symbol=samples_per_symbol,
            excess_bw=EXCESS_BW,
            verbose=False,
            log=False
        )
        self.connect( self, self.mod, self )



class DifferentialEncoder(gr.sync_block):
    """
    A custom block for differential encoding.
    Takes 2-bit input and the previous 3-bit state to compute the new 3-bit state.
    """
    def __init__(self):
        gr.sync_block.__init__(
            self,
            name="DifferentialEncoder",
            in_sig=[np.uint8],  # 输入：2 比特流
            out_sig=[np.uint8]  # 输出：3 比特流
        )
        self.prev_point = 0  # 初始星座点索引（0 对应 8PSK 的第一个点）

        # 定义 2 比特输入到索引增量的映射
        self.phase_table = {
            0b00: +1,  # +π/4
            0b01: +3,  # +3π/4
            0b10: -1,  # -π/4
            0b11: -3   # -3π/4
        }

    def work(self, input_items, output_items):
        input_bits = input_items[0]  # 输入的 2 比特流
        output_bits = output_items[0]  # 输出的 3 比特流

        for i in range(len(input_bits)):
            # 获取当前输入的 2 比特
            delta = self.phase_table[input_bits[i]]  # 查表获取索引增量

            # 计算新的星座点索引
            new_point = (self.prev_point + delta) % 8  # 确保索引在 [0, 7] 范围内
            output_bits[i] = new_point  # 写入输出
            self.prev_point = new_point  # 更新上一次的星座点索引

        return len(output_bits)

class TransmitterPI4DQPSK(gr.hier_block2):
    modname = str(ModTypes.PI4DQPSK)
    bits = ModTypes.PI4DQPSK.bits
    def __init__(self, samples_per_symbol=SAMPLING_RATE, excess_bw=EXCESS_BW):
        gr.hier_block2.__init__(self, "transmitter_pi4dqpsk",
            gr.io_signature(1, 1, gr.sizeof_char),  # 输入：字节流
            gr.io_signature(1, 1, gr.sizeof_gr_complex))  # 输出：复数基带信号

        # 解包字节流为 2 比特流
        self.unpacker = blocks.packed_to_unpacked_bb(2, gr.GR_MSB_FIRST)

        # 差分编码模块
        self.diff_encoder = DifferentialEncoder()

        # 将差分编码后的比特流重新打包为字节流
        self.packer = blocks.unpacked_to_packed_bb(3, gr.GR_MSB_FIRST)  # 每 3 比特打包为 1 字节

        # PSK 调制器
        self.psk_mod = digital.psk.psk_mod(
            constellation_points=8,  # 使用 8PSK 星座点
            mod_code="gray",
            differential=False,  # 差分逻辑已在前面实现
            samples_per_symbol=samples_per_symbol,
            excess_bw=excess_bw,
            verbose=False,
            log=False
        )

        # 连接模块
        self.connect(self, self.unpacker, self.diff_encoder, self.packer, self.psk_mod, self)


class TransmitterPSK8(gr.hier_block2):
    modname = str(ModTypes.PSK8)
    bits = ModTypes.PSK8.bits
    def __init__(self, samples_per_symbol=SAMPLING_RATE):
        gr.hier_block2.__init__(self, "transmitter_{0}".format(self.modname),
            gr.io_signature(1, 1, gr.sizeof_char),
            gr.io_signature(1, 1, gr.sizeof_gr_complex))
        self.mod = digital.psk.psk_mod(
            constellation_points=8,
            mod_code="gray",
            differential=False,
            samples_per_symbol=samples_per_symbol,
            excess_bw=EXCESS_BW,
            verbose=False,
            log=False
        )
        self.connect( self, self.mod, self )

class TransmitterQAM16(TransmitterMapper):
    modname = str(ModTypes.QAM16)
    bits = ModTypes.QAM16.bits
    def __init__(self, samples_per_symbol=SAMPLING_RATE):
        gr.hier_block2.__init__(self, "transmitter_{0}".format(self.modname),
            gr.io_signature(1, 1, gr.sizeof_char),
            gr.io_signature(1, 1, gr.sizeof_gr_complex))
        
        # make qam16 signal modulator
        self.constellation = digital.constellation_rect(
            digital.qam.qam_constellation(16).points(),
            [0, 1, 3, 2, 7, 6, 4, 5, 15, 14, 12, 13, 8, 9, 11, 10],
            4, 4, 4, 1.0, 1.0
        )
        self.mod = digital.generic_mod(
            self.constellation,
            samples_per_symbol=samples_per_symbol,
            verbose=False,
            log=False
        )

        self.connect( self, self.mod, self )


class TransmitterQAM64(TransmitterMapper):
    modname = str(ModTypes.QAM64)
    bits = ModTypes.QAM64.bits
    def __init__(self, samples_per_symbol=SAMPLING_RATE):
        gr.hier_block2.__init__(self, "transmitter_{0}".format(self.modname),
            gr.io_signature(1, 1, gr.sizeof_char),
            gr.io_signature(1, 1, gr.sizeof_gr_complex))
        
        # make QAM64 signal modulator
        self.constellation = digital.constellation_rect(
            digital.qam.qam_constellation(64).points(),
            [0, 32, 8, 40, 3, 35, 11, 43,
             48, 16, 56, 24, 51, 19, 59, 27,
             12, 44, 4, 36, 15, 47, 7, 39,
             60, 28, 52, 20, 63, 31, 55, 23,
             2, 34, 10, 42, 1, 33, 9, 41,
             50, 18, 58, 26, 49, 17, 57, 25,
             14, 46, 6, 38, 13, 45, 5, 37,
             62, 30, 54, 22, 61, 29, 53, 21],
            6, 6, 6, 1.0, 1.0
        )
        self.mod = digital.generic_mod(
            self.constellation,
            samples_per_symbol=samples_per_symbol,
            excess_bw=EXCESS_BW,
            verbose=False,
            log=False
        )

        self.connect( self, self.mod, self )


class TransmitterMSK(gr.hier_block2):
    modname = "MSK"
    bits = 1

    def __init__(self, samples_per_symbol=SAMPLING_RATE):
        gr.hier_block2.__init__(self, "transmitter_msk",
            gr.io_signature(1, 1, gr.sizeof_char),
            gr.io_signature(1, 1, gr.sizeof_gr_complex))
        self.mod = digital.gmsk_mod(
            samples_per_symbol=samples_per_symbol,
            bt=BT_MAX
        )
        self.connect(self, self.mod, self)


class TransmitterFSK(gr.hier_block2):
    modname = "FSK"
    bits = 1
    def __init__(self, samples_per_symbol=SAMPLING_RATE):
        gr.hier_block2.__init__(self, "transmitter_gfsk",
            gr.io_signature(1, 1, gr.sizeof_char),
            gr.io_signature(1, 1, gr.sizeof_gr_complex))
        self.mod = digital.gfsk_mod(
            samples_per_symbol=samples_per_symbol, 
            sensitivity=0.35, 
            bt=BT_MAX
        )
        self.connect( self, self.mod, self )

class TransmitterGFSK(gr.hier_block2):
    modname = "GFSK"
    bits = 1
    def __init__(self, samples_per_symbol=SAMPLING_RATE):
        gr.hier_block2.__init__(self, "transmitter_gfsk",
            gr.io_signature(1, 1, gr.sizeof_char),
            gr.io_signature(1, 1, gr.sizeof_gr_complex))
        self.mod = digital.gfsk_mod(
            samples_per_symbol=samples_per_symbol, 
            sensitivity=0.35, 
            bt=BT_NORMAL
        )
        self.connect( self, self.mod, self )

class Transmitter4FSK(gr.hier_block2):
    modname = "4FSK"
    bits = 2
    def __init__(self, samples_per_symbol=SAMPLING_RATE):
        gr.hier_block2.__init__(self, "transmitter_gfsk4",
            gr.io_signature(1, 1, gr.sizeof_char),
            gr.io_signature(1, 1, gr.sizeof_gr_complex))
        self.mod = gfsk4_mod(
            samples_per_symbol=samples_per_symbol, 
            sensitivity=0.35, 
            bt=BT_MAX
        )
        self.connect(self, self.mod, self)

class Transmitter8FSK(gr.hier_block2):
    modname = "8FSK"
    bits = 3
    def __init__(self, samples_per_symbol=SAMPLING_RATE):
        gr.hier_block2.__init__(self, "transmitter_gfsk8",
            gr.io_signature(1, 1, gr.sizeof_char),
            gr.io_signature(1, 1, gr.sizeof_gr_complex))
        self.mod = gfsk8_mod(
            samples_per_symbol=samples_per_symbol, 
            sensitivity=0.35, 
            bt=BT_MAX
        )
        self.connect(self, self.mod, self)

class TransmitterCW(gr.hier_block2):
    modname = "CW"
    bits = 8
    def __init__(self, samples_per_symbol=SAMPLING_RATE):
        gr.hier_block2.__init__(self, "transmitter_cw_morse",
            gr.io_signature(1, 1, gr.sizeof_char),
            gr.io_signature(1, 1, gr.sizeof_gr_complex))
        self.mod = cw_morse_mod(
            sample_rate=48000,
            tone_freq=6000,
            amplitude=0.5,
            samples_per_symbol=(48000/100)*samples_per_symbol/SAMPLING_RATE,
        )
        self.connect(self, self.mod, self)

class TransmitterCPFSK(gr.hier_block2):
    modname = "CPFSK"
    bits = 1
    def __init__(self, samples_per_symbol=SAMPLING_RATE):
        gr.hier_block2.__init__(self, "transmitter_cpfsk",
            gr.io_signature(1, 1, gr.sizeof_char),
            gr.io_signature(1, 1, gr.sizeof_gr_complex))
        self.mod = analog.cpfsk_bc(0.5, 1.0, samples_per_symbol)
        self.connect( self, self.mod, self )


class TransmitterFM(gr.hier_block2):
    modname = "WBFM"
    bits = 20
    def __init__(self, audio_freq=44.1e3, quad_freq=220.5e3, sample_freq=200e3):
        gr.hier_block2.__init__(self, "transmitter_fm",
        gr.io_signature(1, 1, gr.sizeof_float),
        gr.io_signature(1, 1, gr.sizeof_gr_complex))
        self.mod = analog.wfm_tx( audio_rate=audio_freq, quad_rate=quad_freq )
        self.connect( self, self.mod, self )
        self.rate = sample_freq/audio_freq

# 双边带调幅
class TransmitterAM(gr.hier_block2):
    modname = "AM-DSB"
    bits = 1
    def __init__(self, audio_freq=44.1e3, sample_freq=200e3):
        gr.hier_block2.__init__(self, "transmitter_am",
        gr.io_signature(1, 1, gr.sizeof_float),
        gr.io_signature(1, 1, gr.sizeof_gr_complex))
        self.rate = sample_freq/audio_freq
        self.resampler = filter.pfb.arb_resampler_fff(self.rate)
        self.cnv = blocks.float_to_complex()
        self.mul = blocks.multiply_const_cc(1.0)
        self.add = blocks.add_const_cc(1.0)
        self.carrier = analog.sig_source_c(sample_freq, analog.GR_SIN_WAVE, 0e3, 1.0)
        self.mod = blocks.multiply_cc()
        self.connect( self, self.resampler, self.cnv, self.mul, self.add, self.mod, self )
        self.connect( self.carrier, (self.mod,1) )

# 单边带调幅
class TransmitterAMSSB(gr.hier_block2):
    modname = "AM-SSB"
    bits = 1
    def __init__(self, audio_freq=44.1e3, sample_freq=200e3):
        gr.hier_block2.__init__(self, "transmitter_amssb",
        gr.io_signature(1, 1, gr.sizeof_float),
        gr.io_signature(1, 1, gr.sizeof_gr_complex))
        self.rate = sample_freq/audio_freq
        self.resampler = filter.pfb.arb_resampler_fff(self.rate)
        self.mul = blocks.multiply_const_ff(1.0)
        self.add = blocks.add_const_ff(1.0)
        self.src = analog.sig_source_f(sample_freq, analog.GR_SIN_WAVE, 0e3, 1.0)
        self.mod = blocks.multiply_ff()
        #self.filt = filter.fir_filter_ccf(1, firdes.band_pass(1.0, 200e3, 10e3, 60e3, 0.25e3, firdes.WIN_HAMMING, 6.76))
        self.filt = filter.hilbert_fc(401)
        self.connect( self, self.resampler, self.mul, self.add, self.mod, self.filt, self )
        self.connect( self.src, (self.mod,1) )



class TransmitterNone(gr.hier_block2):
    modname = "_none_"
    bits = 1
    def __init__(self, audio_freq=44.1e3, sample_freq=200e3):
        gr.hier_block2.__init__(self, "transmitter_none",
        gr.io_signature(1, 1, gr.sizeof_float),
        gr.io_signature(1, 1, gr.sizeof_gr_complex))
        self.rate = sample_freq/audio_freq
        self.resampler = filter.pfb.arb_resampler_fff(self.rate)
        self.mul = blocks.multiply_const_ff(0.0)
        self.filt = filter.hilbert_fc(401)
        self.connect( self, self.resampler, self.mul, self.filt, self )
