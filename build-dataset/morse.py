morse_code_dict = {
    'A': '.-', 'B': '-...', 'C': '-.-.', 'D': '-..', 'E': '.', 'F': '..-.',
    'G': '--.', 'H': '....', 'I': '..', 'J': '.---', 'K': '-.-', 'L': '.-..',
    'M': '--', 'N': '-.', 'O': '---', 'P': '.--.', 'Q': '--.-', 'R': '.-.',
    'S': '...', 'T': '-', 'U': '..-', 'V': '...-', 'W': '.--', 'X': '-..-',
    'Y': '-.--', 'Z': '--..',
    '0': '-----', '1': '.----', '2': '..---', '3': '...--', '4': '....-',
    '5': '.....', '6': '-....', '7': '--...', '8': '---..', '9': '----.'
}
def text_to_morse(text):
    morse_text = []
    for char in text.upper():
        if char in morse_code_dict:
            morse_text.append(morse_code_dict[char])
    return morse_text




import gnuradio as gr
import numpy as np

dot_duration = 10
dash_duration = 3 * dot_duration
sample_rate = 1000

def generate_ook_signal(morse_sequence):
    t = []
    for symbol in morse_sequence:
        for char in symbol:
            if char == '.':
                t.extend([1] * dot_duration)
                t.extend([0] * dot_duration)  # 点之间的间隔
            elif char == '-':
                t.extend([1] * dash_duration)
                t.extend([0] * dot_duration)  # 划和点之间的间隔
    t = np.array(t)
    ook_source = gr.vector_source_b(t.astype(np.uint8), False)
    return ook_source



from gnuradio import gr, blocks, analog
import numpy as np
import sys

class cw_morse_simulation(gr.top_block):
    def __init__(self):
        gr.top_block.__init__(self)
        text = "HELLO"
        morse_sequence = text_to_morse(text)
        ook_source = generate_ook_signal(morse_sequence)
        samp_rate = 1000
        fc = 10000
        # 将OOK信号转换为浮点数
        float_source = blocks.float_to_char(ook_source)
        # 进行幅度调制（简单的乘法）
        amplitude_modulator = analog.multiply_const_ff(0.5)
        # 生成载波
        carrier_source = analog.sig_source_c(samp_rate, analog.GR_SIN_WAVE, fc, 1)
        # 进行乘法实现上变频
        mixer = blocks.multiply_cc()
        self.connect(float_source, amplitude_modulator)
        self.connect(amplitude_modulator, (mixer, 0))
        self.connect(carrier_source, (mixer, 1))
if __name__ == "__main__":
    try:
        my_top_block = cw_morse_simulation()
        my_top_block.run()
    except KeyboardInterrupt:
        pass