#!/usr/bin/env python
import transmitters as ts
from source_alphabet import source_alphabet_discrete, source_alphabet_continuous
from gnuradio import channels, gr, blocks
from gnuradio.channels import dynamic_channel_model
import numpy as np
import pickle
import random
import os
from pydub import AudioSegment
import threading

'''
Generate dataset with dynamic channel model across range of SNRs
'''

os.chdir(os.path.dirname(os.path.abspath(__file__)))
print("Current dir is: {}".format(os.getcwd()))

apply_channel = True

# The output format looks like this
# {('mod type', SNR): np.array(nvecs_per_key, 2, sample_length), etc}

transmitters = {
    "continuous":[
        ts.TransmitterNone,
        ts.TransmitterAM,
        ts.TransmitterFM,
    ],
    "discrete":[
        ts.TransmitterBPSK, 
        ts.TransmitterQPSK,
        ts.TransmitterPI4DQPSK,
        ts.TransmitterPSK8,
        ts.TransmitterQAM16,
        ts.<PERSON>mitter<PERSON>FSK,
        ts.<PERSON>mitter<PERSON><PERSON>,
        ts.<PERSON>mitter<PERSON>FS<PERSON>,
        ts.Transmitter8FSK,
        ts.TransmitterCW,
    ],
}

# CIFAR-10 has 6000 samples/class. CIFAR-100 has 600. Somewhere in there seems like right order of magnitude
nvecs_per_key = 600 # 每个 (mod, snr, rate) 样品的数量
snr_vals = [0,2,4,6,8,10,12,14,16,18] # 信噪比 [0,2,4,6,8,10,12,14,16,18]
rate_vals = [4,8,12,16,20,24,28,32] # 采样率 [4,8,12,16,20,24,28,32]
sample_lengths = [1024] # [512, 1024, 2048, 4096, 8192] # 采样长度

fs = 200e3 # 采样频率

print_lock = threading.Lock()

def thread_safe_print(*args, **kwargs):
    with print_lock:
        print(*args, **kwargs)

def generate_samples(sample_length):
    duration = sample_length/fs # 采样时间长度
    dataset = {}
    for snr in snr_vals:
        for rate in rate_vals:
            for alphabet_type in transmitters.keys():
                for i,mod_type in enumerate(transmitters[alphabet_type]):
                    mod_type_key = bytes(mod_type.modname, 'utf-8')
                    dataset[(mod_type_key, snr, rate)] = np.zeros([nvecs_per_key, 2, sample_length], dtype=np.int16)
                    # moar vectors!
                    insufficient_modsnr_vectors = True
                    modvec_indx = 0
                    thread_safe_print("sample length is ", sample_length, ", snr is ", snr, ", rate is ", rate, ", mod_type is ", mod_type.modname)
                    while insufficient_modsnr_vectors:
                        tx_len = sample_length*rate*mod_type.bits
                        if alphabet_type == "discrete":
                            src = source_alphabet_discrete(tx_len, False)
                            mod = mod_type(rate)
                        else:
                            src = source_alphabet_continuous(tx_len)
                            mod = mod_type(sample_freq=200e3*(rate/4)) # base is 4
                        fD = 1
                        delays = [0.0, 0.9, 1.7]
                        mags = [1, 0.8, 0.3]
                        ntaps = 8
                        noise_amp = 10**(-snr/10.0)

                        chan = dynamic_channel_model(
                            fs,     # double samp_rate 输入采样率（以 Hz 为单位）
                            0.01,    # double sro_std_dev 采样率漂移过程每个样本的标准偏差（以 Hz 为单位）
                            50,     # double sro_max_dev 最大采样率偏移量（以 Hz 为单位）
                            0.01,   # double cfo_std_dev 每个样品的载流子频率漂移过程标准偏差（以 Hz 为单位）
                            100e3,  # double cfo_max_dev 最大载波频率偏移（以 Hz 为单位）
                            8,      # unsigned int N 频率选择性衰落仿真中使用的正弦波数量
                            fD,     # double doppler_freq 衰落模拟中使用的最大多普勒频率，单位为 Hz
                            True,  #  bool LOS_model 定义淡化模型是否应包含一行网站组件。LOS->Rician， NLOS->Rayleigh
                            4,    # float K Rician K 因子，模型中镜面反射功率与漫反射功率的比率
                            delays, # pmt_vector_float delays 构成 Power Delay 配置文件的小数样本延迟列表
                            mags,   # pmt_vector_float mags 对应于 Power delay 配置文件中每个延迟时间的幅度列表
                            ntaps,  # int ntaps_mpath 用于插入功率延迟配置文件的滤波器的长度。PDP 中的延迟必须介于 0 和 ntaps_mpath 之间，小数延迟将仅对该滤波器的宽度进行正弦插值。
                            noise_amp,  # double noise_amp 指定 AWGN 进程的标准偏差
                            0x1337 # double noise_seed 噪声源的随机数生成器种子。
                        )

                        snk = blocks.vector_sink_c()

                        tb = gr.top_block()

                        # connect blocks
                        if apply_channel:
                            tb.connect(src, mod, chan, snk)
                        else:
                            tb.connect(src, mod, snk)
                        tb.run()

                        raw_output_vector = np.array(snk.data(), dtype=np.complex64)
                        # start the sampler some random time after channel model transients (arbitrary values here)
                        sampler_indx = random.randint(50, 500)
                        while sampler_indx + sample_length < len(raw_output_vector) and modvec_indx < nvecs_per_key:
                            sampled_vector = raw_output_vector[sampler_indx:sampler_indx+sample_length]

                            # Scale to int16 range
                            max_val = np.max(np.abs(sampled_vector))
                            sampled_vector = sampled_vector / max_val * 32767 # 32767 is max int16 value

                            dataset[(mod_type_key, snr, rate)][modvec_indx,0,:] = np.real(sampled_vector)
                            dataset[(mod_type_key, snr, rate)][modvec_indx,1,:] = np.imag(sampled_vector)
                            # bound the upper end very high so it's likely we get multiple passes through
                            # independent channels
                            # thread_safe_print("tx_len = {}, sample_length = {}, len(raw_output_vector) = {}, sampler_indx = {}, modvec_indx = {}".format(tx_len, sample_length, len(raw_output_vector), sampler_indx, modvec_indx))

                            upper = round(len(raw_output_vector)*0.05)
                            if sample_length+1 > upper:
                                upper = sample_length+100
                            sampler_indx += random.randint(sample_length, upper)

                            if mod_type.modname == 'WBFM':
                                # Calculate zero-centered normalized instantaneous amplitude spectrum density maximum
                                amp = np.sqrt(np.real(sampled_vector) ** 2 + np.imag(sampled_vector) ** 2)
                                sum_amp = np.sum(amp)
                                namp = (len(amp) * amp / sum_amp - 1)
                                fft_result = np.abs(np.fft.fft(namp)) ** 2
                                gamma_max = np.max(fft_result)
                                # thread_safe_print("Max zero-centered normalized instantaneous amplitude spectrum density: {}".format(gamma_max))
                                # 过滤掉 gamma_max 小于 1 的样本
                                if gamma_max < 1.2:
                                    continue

                            modvec_indx += 1

                        if modvec_indx == nvecs_per_key:
                            # we're all done
                            insufficient_modsnr_vectors = False
    thread_safe_print("sample length is ", sample_length, ", all done. writing to disk")
    with open("RadioSamples-rx{}.dat".format(sample_length), "wb") as f:
        pickle.dump(dataset, f)
        thread_safe_print("sample length is ", sample_length, ", writing to disk is done")

threads = []
for sample_length in sample_lengths:
    t = threading.Thread(target=generate_samples, args=(sample_length,))
    threads.append(t)
    t.start()

for t in threads:
    t.join()
