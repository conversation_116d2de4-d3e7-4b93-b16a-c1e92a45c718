#
# GFSK modulation and demodulation.
#
#
# Copyright 2005-2007,2012 Free Software Foundation, Inc.
#
# This file is part of GNU Radio
#
# SPDX-License-Identifier: GPL-3.0-or-later
#
#


# See gnuradio-examples/python/digital for examples
from math import log as ln

import numpy

from gnuradio import gr
from gnuradio import analog
from gnuradio import blocks, filter, digital
from gnuradio.digital import modulation_utils


# default values (used in __init__ and add_options)
_def_samples_per_symbol = 2
_def_sensitivity = 1
_def_bt = 0.35
_def_verbose = False
_def_log = False
_def_do_unpack = True

_def_gain_mu = None
_def_mu = 0.5
_def_freq_error = 0.0
_def_omega_relative_limit = 0.005


# FIXME: Figure out how to make GFSK work with pfb_arb_resampler_fff for both
# transmit and receive so we don't require integer samples per symbol.


# /////////////////////////////////////////////////////////////////////////////
#                              GFSK modulator
# /////////////////////////////////////////////////////////////////////////////

class gfsk4_mod(gr.hier_block2):
    """
    Hierarchical block for Gaussian Frequency Shift Key (GFSK)
    modulation.

    The input is a byte stream (unsigned char) and the
    output is the complex modulated signal at baseband.

    Args:
        samples_per_symbol: samples per baud >= 2 (integer)
        bt: Gaussian filter bandwidth * symbol time (float)
        verbose: Print information about modulator? (bool)
        debug: Print modualtion data to files? (bool)
        unpack: Unpack input byte stream? (bool)
    """

    def __init__(self,
                 samples_per_symbol=_def_samples_per_symbol,
                 sensitivity=_def_sensitivity,
                 bt=_def_bt,
                 verbose=_def_verbose,
                 log=_def_log,
                 do_unpack=_def_do_unpack):

        gr.hier_block2.__init__(self, "gfsk4_mod",
                                # Input signature
                                gr.io_signature(1, 1, gr.sizeof_char),
                                gr.io_signature(1, 1, gr.sizeof_gr_complex))  # Output signature

        samples_per_symbol = int(samples_per_symbol)
        self._samples_per_symbol = samples_per_symbol
        self._bt = bt
        self._differential = False

        if not isinstance(samples_per_symbol, int) or samples_per_symbol < 2:
            raise TypeError(
                "samples_per_symbol must be an integer >= 2, is %r" % (samples_per_symbol,))

        ntaps = 4 * samples_per_symbol			# up to 3 bits in filter at once
        # sensitivity = (pi / 2) / samples_per_symbol	# phase change per bit = pi / 2

        # Turn it into NRZ data.
        #self.nrz = digital.bytes_to_syms()
        self.nrz = digital.chunks_to_symbols_bf([-3, -1, 1, 3])

        # Form Gaussian filter
        # Generate Gaussian response (Needs to be convolved with window below).
        self.gaussian_taps = filter.firdes.gaussian(
            1.0,		       # gain
            samples_per_symbol,    # symbol_rate
            bt,		       # bandwidth * symbol time
            ntaps                  # number of taps
        )

        self.sqwave = (1,) * samples_per_symbol       # rectangular window
        self.taps = numpy.convolve(numpy.array(
            self.gaussian_taps), numpy.array(self.sqwave))
        self.gaussian_filter = filter.interp_fir_filter_fff(
            samples_per_symbol, self.taps)

        # FM modulation
        self.fmmod = analog.frequency_modulator_fc(sensitivity)

        # small amount of output attenuation to prevent clipping USRP sink
        self.amp = blocks.multiply_const_cc(0.999)

        if verbose:
            self._print_verbage()

        if log:
            self._setup_logging()

        # Connect & Initialize base class
        if do_unpack:
            self.unpack = blocks.packed_to_unpacked_bb(2, gr.GR_MSB_FIRST)
            self.connect(self, self.unpack, self.nrz,
                         self.gaussian_filter, self.fmmod, self.amp, self)
        else:
            self.connect(self, self.nrz, self.gaussian_filter,
                         self.fmmod, self.amp, self)

    def samples_per_symbol(self):
        return self._samples_per_symbol

    @staticmethod
    # staticmethod that's also callable on an instance
    def bits_per_symbol(self=None):
        return 2

    def _print_verbage(self):
        print("bits per symbol = %d" % self.bits_per_symbol())
        print("Gaussian filter bt = %.2f" % self._bt)

    def _setup_logging(self):
        print("Modulation logging turned on.")
        self.connect(self.nrz,
                     blocks.file_sink(gr.sizeof_float, "nrz.dat"))
        self.connect(self.gaussian_filter,
                     blocks.file_sink(gr.sizeof_float, "gaussian_filter.dat"))
        self.connect(self.fmmod,
                     blocks.file_sink(gr.sizeof_gr_complex, "fmmod.dat"))

    @staticmethod
    def add_options(parser):
        """
        Adds GFSK modulation-specific options to the standard parser
        """
        parser.add_option("", "--bt", type="float", default=_def_bt,
                          help="set bandwidth-time product [default=%default] (GFSK)")

    @staticmethod
    def extract_kwargs_from_options(options):
        """
        Given command line options, create dictionary suitable for passing to __init__
        """
        return modulation_utils.extract_kwargs_from_options(gfsk4_mod.__init__,
                                                            ('self',), options)


#
# Add these to the mod/demod registry
#
modulation_utils.add_type_1_mod('gfsk4', gfsk4_mod)
