import numpy as np
from gnuradio import gr


class DebugBlockBB(gr.sync_block):
    def __init__(self, name: str, open :bool):
        gr.sync_block.__init__(self,
            name="debug_block_bb",
            in_sig=[np.uint8],
            out_sig=[np.uint8])
        self.open = open
        self.name = name
        
    def work(self, input_items, output_items):
        if self.open:
            print("Debug block: {0}\n       size:{1}\n{2}\n".format(
                  self.name, len(input_items[0]), input_items[0]))
        output_items[0][:] = input_items[0][:]
        return len(output_items[0])



class DebugBlockFF(gr.sync_block):
    def __init__(self, name: str, open :bool):
        gr.sync_block.__init__(self,
            name="debug_block_ff",
            in_sig=[np.float32],
            out_sig=[np.float32])
        self.open = open
        self.name = name
        
    def work(self, input_items, output_items):
        if self.open:
            print("Debug block: {0}\n       size:{1}\n{2}\n".format(
                  self.name, len(input_items[0]), input_items[0]))
        output_items[0][:] = input_items[0][:]
        return len(output_items[0])


class DebugBlockCC(gr.sync_block):
    def __init__(self, name: str, open :bool):
        gr.sync_block.__init__(self,
            name="debug_block_cc",
            in_sig=[np.complex64],
            out_sig=[np.complex64])
        self.open = open
        self.name = name
        
    def work(self, input_items, output_items):
        if self.open:
            print("Debug block: {0}\n       size:{1}\n{2}\n".format(
                  self.name, len(input_items[0]), input_items[0]))
        output_items[0][:] = input_items[0][:]
        return len(output_items[0])
