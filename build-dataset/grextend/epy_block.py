"""
Morse code vector source
"""

#  epy_block_0.py
#  revised 09/10/2019 - finish code table
#  revised 09/11/2019 - test for bad character
#  revised 09/27/2019 - get input from a Message Edit block (code from Volker Schroer dl1ksv)

import numpy as np
from gnuradio import gr

import pmt

textboxValue = ""

Morse = {
    "A": [1, 0, 1, 1, 1],
    "B": [1, 1, 1, 0, 1, 0, 1, 0, 1],
    "C": [1, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1],
    "D": [1, 1, 1, 0, 1, 0, 1],
    "E": [1],
    "F": [1, 0, 1, 0, 1, 1, 1, 0, 1],
    "G": [1, 1, 1, 0, 1, 1, 1, 0, 1],
    "H": [1, 0, 1, 0, 1, 0, 1],
    "I": [1, 0, 1],
    "J": [1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1],
    "K": [1, 1, 1, 0, 1, 0, 1, 1, 1],
    "L": [1, 0, 1, 1, 1, 0, 1, 0, 1],
    "M": [1, 1, 1, 0, 1, 1, 1],
    "N": [1, 1, 1, 0, 1],
    "O": [1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1],
    "P": [1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1],
    "Q": [1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1],
    "R": [1, 0, 1, 1, 1, 0, 1],
    "S": [1, 0, 1, 0, 1],
    "T": [1, 1, 1],
    "U": [1, 0, 1, 0, 1, 1, 1],
    "V": [1, 0, 1, 0, 1, 0, 1, 1, 1],
    "W": [1, 0, 1, 1, 1, 0, 1, 1, 1],
    "X": [1, 1, 1, 0, 1, 0, 1, 0, 1, 1, 1],
    "Y": [1, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 1, 1],
    "Z": [1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1],
    " ": [0],  # space
    "1": [1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1],
    "2": [1, 0, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1],
    "3": [1, 0, 1, 0, 1, 0, 1, 1, 1, 0, 1, 1, 1],
    "4": [1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 1],
    "5": [1, 0, 1, 0, 1, 0, 1, 0, 1],
    "6": [1, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1],
    "7": [1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 1],
    "8": [1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1],
    "9": [1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1],
    "0": [1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1],
    ".": [1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1],  # period
    ",": [1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 1, 1, 1, 0, 1, 1, 1],  # comma
    ":": [1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 1],  # colon
    "?": [1, 0, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1],  # question
    "'": [1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1],  # apostrophe
    "-": [1, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 1],  # dash or minus
    "/": [1, 1, 1, 0, 1, 0, 1, 0, 1, 1, 1, 0, 1],  # slash
    "(": [1, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1],  # left parenthesis
    ")": [1, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1],  # right parenthesis
    "\"": [1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 1, 1, 1, 0, 1],  # quote
    "=": [1, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 1],  # equals
    "+": [1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1],  # plus
    "@": [1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1],  # at sign (@)
    "!": [1, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 1, 1],  # exclamation point
    "&": [1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 1],  # ampersand (also prosign for 'WAIT')
    ";": [1, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1],  # semicolon
    "_": [1, 0, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1],  # underscore
    "$": [1, 0, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 1, 1, 1]  # dollar sign
}


def string_to_morse_code(input_string):
    morse_dict = {
        'A': '.-', 'B': '-...', 'C': '-.-.', 'D': '-..', 'E': '.', 'F': '..-.', 'G': '--.', 'H': '....', 'I': '..', 'J': '.---', 'K': '-.-', 'L': '.-..',
        'M': '--', 'N': '-.', 'O': '---', 'P': '.--.', 'Q': '--.-', 'R': '.-.', 'S': '...', 'T': '-', 'U': '..-', 'V': '...-', 'W': '.--', 'X': '-..-',
        'Y': '-.--', 'Z': '--..', '0': '-----', '1': '.----', '2': '..---', '3': '...--', '4': '....-', '5': '.....', '6': '-....', '7': '--...',
        '8': '---..', '9': '----.', ' ': ' '
    }
    morse_code = []
    for char in input_string.upper():
        if char in morse_dict:
            morse_code.append(morse_dict[char])
            morse_code.append(' ')  # 每个字符之间添加一个空格作为间隔
    return ''.join(morse_code).strip()


class mc_sync_block(gr.basic_block):
    """
    reads input from a message port
    generates a vector of Morse code bits
    """
    def __init__(self):
        gr.basic_block.__init__(self,
            name="Morse code vector source",
            in_sig=[np.uint8],
            out_sig=[np.uint8])

    def encode(self, input_items):
        bit_stream = []
        textboxValue = ""
        if len(input_items) > 0 and len(input_items[0]) > 0:
            textboxValue = input_items[0].tobytes().decode('utf-8')

        if len(textboxValue) <= 0:
            return bit_stream

        for in0 in textboxValue:
            # get next char
            inChar = str(in0)
            # convert to upper case
            ch = inChar.upper()
            # test for character in table
            if not (ch in Morse):
                ch = "?"  # replace bad character with a '?'
            # build vector
            _dots = Morse.get(ch)
            bit_stream.extend(_dots)
            bit_stream.extend([0,0,0])  # letter space

        bit_stream.extend([0,0,0,0])  # finish with word space
        return bit_stream

    def general_work(self, input_items, output_items):
        [nin, nout] = self.work(input_items, output_items)
        self.consume_each(nin)
        return nout

    def work(self, input_items, output_items):
        arr = self.encode(input_items)
        if len(arr) == 0:
            return 0
        
        nout = 0
        if len(arr) > len(output_items[0]):
            output_items[0][:] = arr[:len(output_items[0])]
            nout = len(output_items[0])
        else:
            output_items[0][:len(arr)] = arr[:]
            nout = len(arr)
        return len(input_items[0]), nout

    def forecast(self, noutput_items, ninputs):
        ninput_items_required = [0] * ninputs
        for i in range(ninputs):
            ninput_items_required[i] = noutput_items//16
            if ninput_items_required[i] == 0:
                ninput_items_required[i] = 1
        return ninput_items_required
