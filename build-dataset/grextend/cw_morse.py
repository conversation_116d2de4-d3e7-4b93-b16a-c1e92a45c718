#
# GFSK modulation and demodulation.
#
#
# Copyright 2005-2007,2012 Free Software Foundation, Inc.
#
# This file is part of GNU Radio
#
# SPDX-License-Identifier: GPL-3.0-or-later
#
#


# See gnuradio-examples/python/digital for examples
from math import log as ln

import numpy

from gnuradio import gr
from gnuradio import analog
from gnuradio import blocks, filter, digital
from gnuradio.filter import firdes

from gnuradio.digital import modulation_utils
from .epy_block import mc_sync_block
from .debug_block import DebugBlockBB,DebugBlockCC,DebugBlockFF


# default values (used in __init__ and add_options)
_def_sample_rate = 48000
_def_samples_per_symbol = _def_sample_rate/10
_def_sensitivity = 1
_def_bt = 0.35
_def_verbose = False
_def_log = False
_def_do_unpack = True

_def_gain_mu = None
_def_mu = 0.5
_def_freq_error = 0.0
_def_omega_relative_limit = 0.005


# FIXME: Figure out how to make GFSK work with pfb_arb_resampler_fff for both
# transmit and receive so we don't require integer samples per symbol.


# /////////////////////////////////////////////////////////////////////////////
#                              GFSK modulator
# /////////////////////////////////////////////////////////////////////////////

class cw_morse_mod(gr.hier_block2):
    '''
    A class to implement Continuous Wave (CW) Morse modulation using GNU Radio.

    Attributes:
        _samples_per_symbol (int): Number of samples per symbol.
        _bt (float): Bandwidth-time product for the Gaussian filter.
        _differential (bool): Indicates if differential encoding is used.
        nrz (digital.chunks_to_symbols_bf): Converts bytes to NRZ symbols.
        gaussian_taps (list): Taps for the Gaussian filter.
        sqwave (tuple): Rectangular window for convolution.
        taps (numpy.ndarray): Convolved Gaussian filter taps.
        gaussian_filter (filter.interp_fir_filter_fff): Gaussian filter block.
        fmmod (analog.frequency_modulator_fc): Frequency modulator block.
        amp (blocks.multiply_const_cc): Amplitude scaling block.
        unpack (blocks.packed_to_unpacked_bb): Unpacks packed bits (optional).

    Methods:
        __init__(self, samples_per_symbol, sensitivity, bt, verbose, log, do_unpack):
            Initializes the CW Morse modulation block.
        samples_per_symbol(self):
            Returns the number of samples per symbol.
        bits_per_symbol(self=None):
            Returns the number of bits per symbol (always 1).
        _print_verbage(self):
            Prints modulation parameters for verbose mode.
        _setup_logging(self):
            Sets up logging by connecting internal blocks to file sinks.
        add_options(parser):
            Adds GFSK modulation-specific options to the standard parser.
        extract_kwargs_from_options(options):
            Extracts keyword arguments from command line options for initialization.
    '''

    def __init__(self,
                 sample_rate=_def_sample_rate,
                 tone_freq=600,
                 amplitude=0.5,
                 samples_per_symbol=_def_samples_per_symbol,
                 excess_bw=0.35,
                 do_unpack=_def_do_unpack):

        gr.hier_block2.__init__(self, "cw_morse_mod",
                                # Input signature
                                gr.io_signature(1, 1, gr.sizeof_char),
                                gr.io_signature(1, 1, gr.sizeof_gr_complex))  # Output signature

        samples_per_symbol = int(samples_per_symbol)
        self._samples_per_symbol = samples_per_symbol

        self._sample_rate=sample_rate
        self._tone_freq=tone_freq
        self._amplitude=amplitude

        if not isinstance(samples_per_symbol, int) or samples_per_symbol < 2:
            raise TypeError(
                "samples_per_symbol must be an integer >= 2, is %r" % (samples_per_symbol,))


        self.morse_code = mc_sync_block()

        # 映射模块，将 0 和 1 映射为相应的幅度
        self.uchar_to_float = blocks.uchar_to_float()
        # 乘法模块，设置幅度
        self.mult = blocks.multiply_const_ff(amplitude)

      # 第一个根余弦滤波器（预滤波）
        sps = samples_per_symbol  # 每个符号的样本数
        ntaps = 11 * sps  # 滤波器抽头数
        rrc_taps_pre = filter.firdes.root_raised_cosine(1.0, sps, 1.0, excess_bw, ntaps)
        self.rrc_filter_pre = filter.fir_filter_fff(sps, rrc_taps_pre)
        self.float_to_complex = blocks.float_to_complex()
        # 正弦波发生器，产生音频信号
        self.sine = analog.sig_source_c(sample_rate, analog.GR_SIN_WAVE, tone_freq, 1, 0)
        # 乘法模块，将脉冲序列与正弦波相乘得到音频信号
        self.mult2 = blocks.multiply_cc()
        # 第二个根余弦滤波器（后滤波）
        rrc_taps_post = filter.firdes.root_raised_cosine(1.0, sps, 1.0, excess_bw, ntaps)
        self.rrc_filter_post = filter.fir_filter_fff(sps, rrc_taps_post)
        self.repeat = blocks.repeat(gr.sizeof_char*1, 4)

        self.DebugBlock = DebugBlockCC("cw_morse_mod", True)

        # Connect & Initialize base class
        if do_unpack:
            self.unpack = blocks.packed_to_unpacked_bb(8, gr.GR_MSB_FIRST)
            self.connect(self, self.unpack, self.morse_code, self.repeat, self.uchar_to_float, self.float_to_complex, self.mult2, self)
            self.connect(self.sine, (self.mult2, 1))
        else:
            self.connect(self, self.morse_code, self.repeat, self.uchar_to_float, self.float_to_complex, self.mult2, self)
            self.connect(self.sine, (self.mult2, 1))

    def samples_per_symbol(self):
        return self._samples_per_symbol

    @staticmethod
    def extract_kwargs_from_options(options):
        """
        Given command line options, create dictionary suitable for passing to __init__
        """
        return modulation_utils.extract_kwargs_from_options(cw_morse_mod.__init__,
                                                            ('self',), options)


#
# Add these to the mod/demod registry
#
modulation_utils.add_type_1_mod('cw_morse', cw_morse_mod)
