import unittest
from gnuradio import gr, blocks

class TestUnpackToPackBB(unittest.TestCase):
    def test_unpack_to_pack_bb(self):
        src_data = (0, 1, 0, 1, 1, 0, 1, 0)
        expected_result = (90,)
        src = blocks.vector_source_b(src_data)
        unpacked_to_packed = blocks.unpacked_to_packed_bb(1, gr.GR_MSB_FIRST)
        dst = blocks.vector_sink_b()
        tb = gr.top_block()
        tb.connect(src, unpacked_to_packed)
        tb.connect(unpacked_to_packed, dst)
        tb.run()
        result_data = dst.data()
        self.assertEqual(expected_result, tuple(result_data))


    def test_unpack_to_pack_bb_2(self):
        src_data = (0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0)
        expected_result = (17, 68, 68, 20)
        src = blocks.vector_source_b(src_data)
        unpacked_to_packed = blocks.unpacked_to_packed_bb(2, gr.GR_MSB_FIRST)
        dst = blocks.vector_sink_b()
        tb = gr.top_block()
        tb.connect(src, unpacked_to_packed)
        tb.connect(unpacked_to_packed, dst)
        tb.run()
        result_data = dst.data()
        self.assertEqual(expected_result, tuple(result_data))
    def test_unpack_to_pack_bb_3(self):
        src_data = (0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 1)
        expected_result = (2, 6, 5, 2, 3, 3, 1, 5)
        src = blocks.vector_source_b(src_data)
        unpacked_to_packed = blocks.unpacked_to_packed_bb(1, gr.GR_LSB_FIRST)
        packed_to_unpacked = blocks.packed_to_unpacked_bb(3, gr.GR_LSB_FIRST)
        dst = blocks.vector_sink_b()
        tb = gr.top_block()
        tb.connect(src, unpacked_to_packed, packed_to_unpacked, dst)
        tb.run()
        result_data = dst.data()
        self.assertEqual(expected_result, tuple(result_data))



if __name__ == '__main__':
    unittest.main()